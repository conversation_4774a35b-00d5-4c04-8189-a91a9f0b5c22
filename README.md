# Duct Factory Manager App

A comprehensive mobile application for managing duct installation jobs, built with React Native and Expo.

## Features

### Multi-User System
- **Admin (کارگە)**: Full management access
- **Dispatch (ناردن)**: Job dispatch and tracking
- **Installer 1 (وەستای بەستن یەکەم)**: Installation work management
- **Installer 2 (وەستای بەستن دووەم)**: Installation work management

### Admin Features
- Add new customer jobs with location mapping
- Manage all jobs with filtering and search
- User management with secure access
- Comprehensive dashboard with statistics
- Image upload for job documentation
- Real-time job status updates

### Installer/Dispatch Features
- View assigned jobs
- Mark jobs as completed/not completed
- Access customer contact information
- Navigate to job locations via Google Maps
- View job images in full size
- Monthly statistics and performance tracking

### Technical Features
- **RTL Support**: Full right-to-left layout for Kurdish language
- **Real-time Sync**: Live updates across all users via Supabase
- **Location Services**: GPS integration for job site mapping
- **Image Management**: Camera and gallery integration
- **Secure Authentication**: Role-based access control
- **Offline Capability**: Local storage for critical data

## User Credentials

| Role | Username | Password | Display Name |
|------|----------|----------|--------------|
| Admin | ali | 223344 | کارگە |
| Dispatch | car | 123455 | ناردن |
| Installer 1 | wasta1 | 654321 | وەستای بەستن یەکەم |
| Installer 2 | wasta2 | 123456788 | وەستای بەستن دووەم |

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd aliduct
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your Supabase credentials
   ```

4. **Start the development server**
   ```bash
   npm start
   ```

5. **Run on device**
   - Scan QR code with Expo Go app
   - Or use `npm run android` / `npm run ios`

## Database Schema

### Tables
- `app_users`: User authentication and roles
- `jobs`: Customer jobs and installation tasks
- `places`: Location/city data
- `areas`: Neighborhood/area data

### Key Features
- Row Level Security (RLS) for data protection
- Real-time subscriptions for live updates
- Image storage via Supabase Storage
- Geolocation data for job sites

## App Structure

```
├── contexts/
│   └── AuthContext.tsx          # Authentication management
├── navigation/
│   └── AppNavigator.tsx         # Navigation structure
├── screens/
│   ├── admin/                   # Admin user screens
│   ├── installer/               # Installer user screens
│   ├── dispatch/                # Dispatch user screens
│   ├── LoginScreen.tsx          # Authentication screen
│   └── LoadingScreen.tsx        # Loading state
├── lib/
│   └── supabase.ts             # Database configuration
└── types/
    └── supabase.ts             # TypeScript definitions
```

## Key Workflows

### Admin Workflow
1. Login → Jobs List (main screen)
2. Add new customers with location mapping
3. Manage job statuses and assignments
4. Send jobs to dispatch or installers
5. Monitor progress via dashboard

### Installer Workflow
1. Login → Assigned Jobs
2. View job details and customer info
3. Navigate to job location
4. Mark jobs as completed/not completed
5. View performance statistics

### Dispatch Workflow
1. Login → Dispatch Jobs
2. Manage assigned dispatch tasks
3. Update job completion status
4. Track monthly performance

## Technologies Used

- **React Native**: Cross-platform mobile development
- **Expo**: Development platform and tools
- **Supabase**: Backend-as-a-Service (database, auth, storage)
- **TypeScript**: Type-safe development
- **React Navigation**: Navigation management
- **Expo Location**: GPS and mapping services
- **Expo Image Picker**: Camera and gallery access
- **React Native Maps**: Map integration

## Security Features

- Secure password-based authentication
- Role-based access control
- Row Level Security (RLS) in database
- Encrypted local storage for credentials
- Environment variable protection

## Performance Optimizations

- Real-time data synchronization
- Efficient image handling and caching
- Optimized database queries
- Lazy loading for large datasets
- Memory management for maps and images

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For technical support or feature requests, please contact the development team.

## License

This project is proprietary software developed for Duct Factory management.
