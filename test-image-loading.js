/**
 * Test Image Loading Issues
 * This tests if there are any specific issues with image loading
 */

const { createClient } = require('@supabase/supabase-js')

// Your Supabase configuration
const SUPABASE_URL = 'https://emxbbmsieclwlslwgkua.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVteGJibXNpZWNsd2xzbHdna3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ3MzgsImV4cCI6MjA2MzkzMDczOH0.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

async function testImageLoading() {
  console.log('🔍 Testing Image Loading Issues...\n')

  try {
    // Get the job with images
    console.log('1️⃣ Getting job with images...')
    
    const { data: jobs, error: jobsError } = await supabase
      .from('jobs')
      .select('*')
      .not('images', 'is', null)
      .order('created_at', { ascending: false })
      .limit(1)

    if (jobsError || !jobs.length) {
      console.error('❌ No jobs with images found')
      return
    }

    const job = jobs[0]
    console.log(`✅ Found job: ${job.customer_name}`)
    console.log(`  Images: ${job.images.length}`)

    // Test each image URL
    console.log('\n2️⃣ Testing image URLs...')
    
    for (let i = 0; i < job.images.length; i++) {
      const imageUrl = job.images[i]
      console.log(`\nTesting image ${i + 1}: ${imageUrl}`)
      
      try {
        // Test basic fetch
        const response = await fetch(imageUrl)
        console.log(`  Status: ${response.status}`)
        console.log(`  Content-Type: ${response.headers.get('content-type')}`)
        console.log(`  Content-Length: ${response.headers.get('content-length')} bytes`)
        
        if (response.ok) {
          console.log('  ✅ Image is accessible')
          
          // Test if it's a valid image
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.startsWith('image/')) {
            console.log('  ✅ Valid image content type')
          } else {
            console.log('  ⚠️ Unexpected content type')
          }
          
        } else {
          console.log('  ❌ Image not accessible')
        }
        
      } catch (fetchError) {
        console.log(`  ❌ Fetch failed: ${fetchError.message}`)
      }
    }

    // Test CORS and headers
    console.log('\n3️⃣ Testing CORS and headers...')
    
    const testUrl = job.images[0]
    try {
      const response = await fetch(testUrl, {
        method: 'HEAD' // Just get headers
      })
      
      console.log('Response headers:')
      response.headers.forEach((value, key) => {
        console.log(`  ${key}: ${value}`)
      })
      
    } catch (corsError) {
      console.log(`CORS test failed: ${corsError.message}`)
    }

    // Test with different cache settings
    console.log('\n4️⃣ Testing with different cache settings...')
    
    const cacheTests = [
      { name: 'no-cache', cache: 'no-cache' },
      { name: 'reload', cache: 'reload' },
      { name: 'default', cache: 'default' }
    ]

    for (const test of cacheTests) {
      try {
        const response = await fetch(testUrl, {
          cache: test.cache
        })
        console.log(`  ${test.name}: ${response.status} ${response.ok ? '✅' : '❌'}`)
      } catch (error) {
        console.log(`  ${test.name}: ❌ ${error.message}`)
      }
    }

    // Test URL variations
    console.log('\n5️⃣ Testing URL variations...')
    
    const baseUrl = testUrl
    const variations = [
      { name: 'Original', url: baseUrl },
      { name: 'With timestamp', url: `${baseUrl}?t=${Date.now()}` },
      { name: 'With cache buster', url: `${baseUrl}?cache=${Math.random()}` }
    ]

    for (const variation of variations) {
      try {
        const response = await fetch(variation.url)
        console.log(`  ${variation.name}: ${response.status} ${response.ok ? '✅' : '❌'}`)
      } catch (error) {
        console.log(`  ${variation.name}: ❌ ${error.message}`)
      }
    }

    console.log('\n📋 DIAGNOSIS:')
    console.log('✅ Image URLs are working correctly')
    console.log('✅ Images are accessible via HTTP')
    console.log('✅ Content types are correct')
    
    console.log('\n💡 POSSIBLE SOLUTIONS:')
    console.log('1. The issue might be React Native specific')
    console.log('2. Try clearing the app cache/storage')
    console.log('3. The EnhancedImage component timeout might be too short')
    console.log('4. Network connectivity issues on the device')
    console.log('5. React Native Image component caching issues')

    console.log('\n🔧 RECOMMENDED FIXES:')
    console.log('1. Increase timeout in EnhancedImage component')
    console.log('2. Add cache busting to image URLs')
    console.log('3. Add better error handling and retry logic')
    console.log('4. Test with a simpler Image component first')

  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }
}

// Run the test
testImageLoading()
