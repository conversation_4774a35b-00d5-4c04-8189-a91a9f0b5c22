/**
 * Test script to verify Supabase Storage setup
 * Run this with: node test-storage-setup.js
 */

const { createClient } = require('@supabase/supabase-js')

// Replace with your actual Supabase URL and anon key
const SUPABASE_URL = 'https://emxbbmsieclwlslwgkua.supabase.co'
const SUPABASE_ANON_KEY = 'your-anon-key-here' // You'll need to get this from your Supabase dashboard

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

async function testStorageSetup() {
  console.log('🧪 Testing Supabase Storage Setup...\n')

  try {
    // Test 1: List buckets
    console.log('1️⃣ Testing bucket access...')
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()
    
    if (bucketsError) {
      console.error('❌ Failed to list buckets:', bucketsError)
      return
    }
    
    console.log('✅ Successfully listed buckets:', buckets.map(b => b.name))
    
    // Test 2: Check if job-images bucket exists
    const jobImagesBucket = buckets.find(bucket => bucket.name === 'job-images')
    if (!jobImagesBucket) {
      console.error('❌ job-images bucket not found')
      return
    }
    
    console.log('✅ job-images bucket found:', {
      public: jobImagesBucket.public,
      fileSizeLimit: jobImagesBucket.file_size_limit,
      allowedMimeTypes: jobImagesBucket.allowed_mime_types
    })

    // Test 3: Test file upload (using a simple text file as test)
    console.log('\n2️⃣ Testing file upload...')
    const testContent = 'This is a test file for storage verification'
    const testFileName = `test-${Date.now()}.txt`
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('job-images')
      .upload(testFileName, testContent, {
        contentType: 'text/plain'
      })

    if (uploadError) {
      console.error('❌ Failed to upload test file:', uploadError)
      return
    }
    
    console.log('✅ Successfully uploaded test file:', uploadData.path)

    // Test 4: Get public URL
    console.log('\n3️⃣ Testing public URL generation...')
    const { data: urlData } = supabase.storage
      .from('job-images')
      .getPublicUrl(uploadData.path)
    
    console.log('✅ Generated public URL:', urlData.publicUrl)

    // Test 5: Clean up - delete test file
    console.log('\n4️⃣ Cleaning up test file...')
    const { error: deleteError } = await supabase.storage
      .from('job-images')
      .remove([uploadData.path])

    if (deleteError) {
      console.error('⚠️ Failed to delete test file:', deleteError)
    } else {
      console.log('✅ Successfully deleted test file')
    }

    console.log('\n🎉 All storage tests passed! Your Supabase Storage is properly configured.')
    
  } catch (error) {
    console.error('💥 Unexpected error during testing:', error)
  }
}

// Run the test
testStorageSetup()
