{"expo": {"name": "Duct Factory Manager", "slug": "aliduct", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "platforms": ["ios", "android"], "extra": {"supportsRTL": true}, "plugins": ["expo-localization", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "This app needs access to location when open and in the background."}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff"}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them.", "cameraPermission": "The app accesses your camera to let you take photos."}]], "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.ductfactorymanager"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.ductfactorymanager", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}}}