# Create a simple working AVD on D: drive
Write-Host "Creating simple AVD on D: drive..." -ForegroundColor Green

# Set environment variables
$env:ANDROID_HOME = "D:\sdk"
$env:ANDROID_SDK_ROOT = "D:\sdk"

# Create AVD directory on D: drive
$avdDir = "D:\android-avd"
$avdName = "Expo_Test_Device"
$avdPath = "$avdDir\$avdName.avd"

# Clean up any existing AVD
Remove-Item -Path "$env:USERPROFILE\.android\avd\$avdName.ini" -Force -ErrorAction SilentlyContinue
Remove-Item -Path $avdPath -Recurse -Force -ErrorAction SilentlyContinue

if (-not (Test-Path $avdDir)) {
    New-Item -ItemType Directory -Path $avdDir -Force | Out-Null
}

if (-not (Test-Path $avdPath)) {
    New-Item -ItemType Directory -Path $avdPath -Force | Out-Null
}

# Create a minimal working config.ini
$configContent = @"
AvdId=Expo_Test_Device
PlayStore.enabled=false
abi.type=x86_64
avd.ini.displayname=Expo Test Device
avd.ini.encoding=UTF-8
disk.dataPartition.size=2147483648
hw.accelerometer=yes
hw.audioInput=yes
hw.battery=yes
hw.camera.back=emulated
hw.camera.front=emulated
hw.cpu.arch=x86_64
hw.cpu.ncore=4
hw.dPad=no
hw.device.manufacturer=Google
hw.device.name=pixel
hw.gps=yes
hw.gpu.enabled=yes
hw.gpu.mode=auto
hw.initialOrientation=Portrait
hw.keyboard=yes
hw.lcd.density=420
hw.lcd.height=1920
hw.lcd.width=1080
hw.mainKeys=no
hw.ramSize=2048
hw.sdCard=yes
hw.sensors.orientation=yes
hw.sensors.proximity=yes
hw.trackBall=no
image.sysdir.1=system-images\android-34\google_apis\x86_64\
runtime.network.latency=none
runtime.network.speed=full
sdcard.size=256M
showDeviceFrame=no
skin.dynamic=yes
skin.name=1080x1920
tag.display=Google APIs
tag.id=google_apis
vm.heapSize=256
"@

$configPath = "$avdPath\config.ini"
$configContent | Out-File -FilePath $configPath -Encoding ASCII -NoNewline

Write-Host "Created config.ini at: $configPath" -ForegroundColor Green

# Create AVD ini file
$defaultAvdDir = "$env:USERPROFILE\.android\avd"
if (-not (Test-Path $defaultAvdDir)) {
    New-Item -ItemType Directory -Path $defaultAvdDir -Force | Out-Null
}

$avdIniContent = @"
avd.ini.encoding=UTF-8
path=D:\android-avd\Expo_Test_Device.avd
target=android-34
"@

$avdIniPath = "$defaultAvdDir\$avdName.ini"
$avdIniContent | Out-File -FilePath $avdIniPath -Encoding ASCII -NoNewline

Write-Host "Created AVD ini at: $avdIniPath" -ForegroundColor Green

Write-Host ""
Write-Host "Simple AVD created successfully on D: drive!" -ForegroundColor Green
Write-Host "Location: $avdPath" -ForegroundColor Cyan
Write-Host "Size: ~2GB" -ForegroundColor Cyan
