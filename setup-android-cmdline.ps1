# PowerShell script to set up Android SDK Command Line Tools
# For SDK Command Line Tools installation

Write-Host "Android SDK Command Line Tools Setup" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Your SDK location
$sdkRoot = "D:\sdk"
$cmdlineToolsPath = "$sdkRoot\commandlinetools-win-13114758_latest"

Write-Host ""
Write-Host "Checking SDK Command Line Tools..." -ForegroundColor Yellow

# Check if the command line tools exist
if (-not (Test-Path $cmdlineToolsPath)) {
    Write-Host "Command line tools not found at: $cmdlineToolsPath" -ForegroundColor Red
    Write-Host "Please verify the path is correct." -ForegroundColor Yellow
    exit 1
}

Write-Host "Found command line tools at: $cmdlineToolsPath" -ForegroundColor Green

# Set up the proper directory structure
$sdkManagerPath = "$cmdlineToolsPath\bin\sdkmanager.bat"
if (-not (Test-Path $sdkManagerPath)) {
    Write-Host "sdkmanager.bat not found. Checking directory structure..." -ForegroundColor Yellow
    
    # The command line tools need to be in a 'latest' subdirectory
    $latestPath = "$sdkRoot\cmdline-tools\latest"
    if (-not (Test-Path "$sdkRoot\cmdline-tools")) {
        New-Item -ItemType Directory -Path "$sdkRoot\cmdline-tools" -Force | Out-Null
    }
    
    if (-not (Test-Path $latestPath)) {
        Write-Host "Moving command line tools to proper location..." -ForegroundColor Yellow
        Move-Item $cmdlineToolsPath $latestPath -Force
        $cmdlineToolsPath = $latestPath
        $sdkManagerPath = "$latestPath\bin\sdkmanager.bat"
    }
}

Write-Host ""
Write-Host "Setting up environment variables..." -ForegroundColor Yellow

try {
    # Set ANDROID_HOME (or ANDROID_SDK_ROOT)
    [Environment]::SetEnvironmentVariable("ANDROID_HOME", $sdkRoot, "User")
    [Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", $sdkRoot, "User")
    Write-Host "Set ANDROID_HOME = $sdkRoot" -ForegroundColor Green
    Write-Host "Set ANDROID_SDK_ROOT = $sdkRoot" -ForegroundColor Green
    
    # Get current PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    
    # Add Android SDK tools to PATH
    $pathsToAdd = @(
        "$cmdlineToolsPath\bin",
        "$sdkRoot\platform-tools",
        "$sdkRoot\emulator",
        "$sdkRoot\tools",
        "$sdkRoot\tools\bin"
    )
    
    $pathUpdated = $false
    foreach ($pathToAdd in $pathsToAdd) {
        if ($currentPath -notlike "*$pathToAdd*") {
            $currentPath = "$currentPath;$pathToAdd"
            $pathUpdated = $true
            Write-Host "Added to PATH: $pathToAdd" -ForegroundColor Green
        } else {
            Write-Host "Already in PATH: $pathToAdd" -ForegroundColor Blue
        }
    }
    
    if ($pathUpdated) {
        [Environment]::SetEnvironmentVariable("PATH", $currentPath, "User")
        Write-Host "Updated PATH environment variable" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "Environment variables set successfully!" -ForegroundColor Green
    Write-Host "Please restart your terminal for changes to take effect." -ForegroundColor Yellow
    
    Write-Host ""
    Write-Host "Next steps after restarting terminal:" -ForegroundColor Cyan
    Write-Host "1. Install platform tools: sdkmanager `"platform-tools`"" -ForegroundColor White
    Write-Host "2. Install build tools: sdkmanager `"build-tools;34.0.0`"" -ForegroundColor White
    Write-Host "3. Install Android platform: sdkmanager `"platforms;android-34`"" -ForegroundColor White
    Write-Host "4. Install emulator: sdkmanager `"emulator`"" -ForegroundColor White
    Write-Host "5. Install system image: sdkmanager `"system-images;android-34;google_apis;x86_64`"" -ForegroundColor White
    Write-Host "6. Create AVD: avdmanager create avd -n test_device -k `"system-images;android-34;google_apis;x86_64`"" -ForegroundColor White
    
} catch {
    Write-Host "Error setting environment variables: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please run this script as Administrator" -ForegroundColor Yellow
}
