# Work Statistics System Documentation

## Overview

The Work Statistics system provides permanent tracking of installer completion history that persists even when admin users delete jobs from the main system. This ensures installers always have access to their complete work history for performance tracking and record-keeping.

## Architecture

### Database Design

#### Primary Table: `work_statistics`
```sql
CREATE TABLE work_statistics (
  id UUID PRIMARY KEY,
  job_id UUID NOT NULL,           -- Reference to original job (not a foreign key)
  installer_id VARCHAR(50) NOT NULL, -- installer1 or installer2
  customer_name VARCHAR(255) NOT NULL,
  place_name VARCHAR(255),
  area_name VARCHAR(255),
  completion_date TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Key Design Decisions:
- **No Foreign Key Constraint**: `job_id` is stored as reference but not as foreign key, allowing original jobs to be deleted
- **Immutable Records**: RLS policies prevent updates/deletes to maintain data integrity
- **Denormalized Data**: Place and area names are stored directly to preserve historical context
- **Automatic Population**: Triggers automatically create records when jobs are completed

### Data Flow

1. **Job Completion**: Installer clicks "تەواو" (Completed) button
2. **Jobs Table Update**: `completion_status` set to 'completed', `completed_at` and `completed_by` fields populated
3. **Automatic Trigger**: Database trigger detects completion and creates permanent record in `work_statistics`
4. **Data Preservation**: Work statistics record includes snapshot of customer name, location at time of completion
5. **Persistent Access**: Installers can always view their work history, regardless of job deletion

## Features

### For Installers
- **Permanent Work History**: View all completed work regardless of job deletion status
- **Month/Year Filtering**: Filter completion history by specific time periods
- **Kurdish RTL Interface**: Full right-to-left layout with Kurdish text
- **Real-time Updates**: New completions automatically appear in statistics

### For Administrators
- **Job Management Freedom**: Delete jobs without affecting installer work history
- **Data Integrity**: Work statistics cannot be modified or deleted
- **Historical Preservation**: Complete audit trail of installer performance

## Implementation Details

### Automatic Data Capture
```sql
-- Trigger function automatically creates work statistics
CREATE TRIGGER trigger_create_work_statistics
    AFTER UPDATE ON jobs
    FOR EACH ROW
    EXECUTE FUNCTION create_work_statistics_on_completion();
```

### Data Captured on Completion:
- Customer name (at time of completion)
- Place name (at time of completion)
- Area name (at time of completion)
- Exact completion timestamp
- Installer who completed the work

### Security & Permissions
- **Row Level Security**: Enabled on work_statistics table
- **Read Access**: Installers can view all work statistics (filtered by app)
- **Write Protection**: No updates or deletes allowed
- **Insert Only**: New records can only be created via trigger

## User Interface

### WorkStatsScreen Features
- **File**: `screens/installer/WorkStatsScreen.tsx`
- **Navigation**: Available as "ئاماری کارکردن" tab for installer users
- **Filtering**: Month and year dropdowns with Kurdish month names
- **Display**: Customer name, completion date/time, location information
- **Summary**: Total completed work count with contextual messaging

### Data Persistence Benefits
- ✅ **Admin Deletes Job**: Work statistics remain intact
- ✅ **Customer Data Changes**: Historical snapshot preserved
- ✅ **Location Updates**: Original completion location maintained
- ✅ **Performance Tracking**: Complete installer work history available

## Migration & Compatibility

### Existing Data
- All previously completed jobs automatically migrated to work_statistics table
- Historical completion data preserved with fallback to `updated_at` if `completed_at` not available
- Installer assignment preserved using `completed_by` or `assigned_installer` fields

### Backward Compatibility
- Original jobs table structure unchanged
- Existing completion tracking continues to work
- Additional work_statistics table provides enhanced persistence

## Database Queries

### Installer Work Statistics
```sql
-- Get all work for specific installer
SELECT * FROM work_statistics 
WHERE installer_id = 'installer1' 
ORDER BY completion_date DESC;

-- Filter by month/year
SELECT * FROM work_statistics 
WHERE installer_id = 'installer1' 
AND EXTRACT(MONTH FROM completion_date) = 6
AND EXTRACT(YEAR FROM completion_date) = 2025;
```

### Performance Optimization
- Indexed on `installer_id` and `completion_date`
- Composite index for common filter combinations
- Efficient queries for large datasets

## Benefits

### Data Integrity
- **Permanent Records**: Work history never lost due to job deletion
- **Audit Trail**: Complete record of installer performance over time
- **Historical Context**: Preserves customer and location data at completion time

### User Experience
- **Reliable Statistics**: Installers can trust their work history is complete
- **Performance Tracking**: Accurate completion counts and timing
- **Historical Reference**: Access to all past work regardless of current job status

### Administrative Flexibility
- **Clean Job Management**: Delete completed jobs without losing statistics
- **Data Archival**: Maintain lean jobs table while preserving work history
- **Compliance**: Meet record-keeping requirements without database bloat

## Monitoring & Maintenance

### Health Checks
- Verify trigger is functioning: Check for new work_statistics records when jobs completed
- Monitor data consistency: Ensure completion counts match between systems
- Performance monitoring: Track query performance on work_statistics table

### Backup Considerations
- Work statistics table contains critical historical data
- Regular backups essential for compliance and audit requirements
- Consider separate backup schedule for statistics vs operational data

This system ensures that installer work statistics remain a permanent, reliable record of performance history while maintaining the flexibility for administrators to manage the operational job database as needed.
