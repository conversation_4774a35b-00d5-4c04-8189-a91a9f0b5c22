# Install Android Emulator Hypervisor Driver v2.2
# This script handles the new installation method

Write-Host "Installing Android Emulator Hypervisor Driver v2.2..." -ForegroundColor Green

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInR<PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "This script needs to run as Administrator." -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator', then run this script again." -ForegroundColor Yellow
    exit 1
}

# Check if the zip file exists in Downloads
$downloadsPath = "$env:USERPROFILE\Downloads\aehd-windows_v2_2_0.zip"
$currentPath = ".\aehd-windows_v2_2_0.zip"

$zipPath = $null
if (Test-Path $downloadsPath) {
    $zipPath = $downloadsPath
    Write-Host "Found driver package in Downloads folder" -ForegroundColor Green
} elseif (Test-Path $currentPath) {
    $zipPath = $currentPath
    Write-Host "Found driver package in current directory" -ForegroundColor Green
} else {
    Write-Host "Driver package not found!" -ForegroundColor Red
    Write-Host "Please download it from: https://github.com/google/android-emulator-hypervisor-driver/releases/download/v2.2/aehd-windows_v2_2_0.zip" -ForegroundColor Yellow
    Write-Host "And place it in your Downloads folder or current directory" -ForegroundColor Yellow
    exit 1
}

# Extract the zip file
$extractPath = "$env:TEMP\aehd-driver"
Write-Host "Extracting driver package..." -ForegroundColor Yellow

if (Test-Path $extractPath) {
    Remove-Item $extractPath -Recurse -Force
}

try {
    Expand-Archive -Path $zipPath -DestinationPath $extractPath -Force
    Write-Host "Extraction completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to extract the package: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Find the silent_install.bat file
$installScript = Get-ChildItem -Path $extractPath -Filter "silent_install.bat" -Recurse | Select-Object -First 1

if (-not $installScript) {
    Write-Host "Could not find silent_install.bat in the package" -ForegroundColor Red
    Write-Host "Package contents:" -ForegroundColor Yellow
    Get-ChildItem -Path $extractPath -Recurse | ForEach-Object { Write-Host "  $($_.FullName)" -ForegroundColor White }
    exit 1
}

Write-Host "Found installation script: $($installScript.FullName)" -ForegroundColor Green

# Run the installation script
Write-Host "Running driver installation..." -ForegroundColor Yellow
Write-Host "Looking for 'STATE: 4 RUNNING' message..." -ForegroundColor Cyan

try {
    $result = & cmd.exe /c "`"$($installScript.FullName)`""
    Write-Host $result -ForegroundColor White
    
    if ($result -like "*STATE: 4 RUNNING*") {
        Write-Host ""
        Write-Host "✅ Driver installed successfully!" -ForegroundColor Green
        Write-Host "✅ Found 'STATE: 4 RUNNING' - Driver is running" -ForegroundColor Green
        Write-Host ""
        Write-Host "🔄 Please restart your computer for the driver to take full effect." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "After restart, you can test the emulator with:" -ForegroundColor Cyan
        Write-Host "emulator -avd Expo_Test_Device" -ForegroundColor White
    } else {
        Write-Host ""
        Write-Host "⚠️  Installation completed but 'STATE: 4 RUNNING' not found." -ForegroundColor Yellow
        Write-Host "The driver may still work. Try restarting and testing the emulator." -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error running installation script: $($_.Exception.Message)" -ForegroundColor Red
}

# Clean up
Write-Host ""
Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
Remove-Item $extractPath -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "Installation process completed!" -ForegroundColor Green
