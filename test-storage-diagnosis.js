/**
 * Storage Diagnosis Script for Aliduct App
 * This script will help identify the exact issue with image uploads
 */

const { createClient } = require('@supabase/supabase-js')

// Your Supabase configuration
const SUPABASE_URL = 'https://emxbbmsieclwlslwgkua.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVteGJibXNpZWNsd2xzbHdna3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ3MzgsImV4cCI6MjA2MzkzMDczOH0.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

async function diagnosisStorageIssue() {
  console.log('🔍 Diagnosing Supabase Storage Issue...\n')

  try {
    // Test 1: Check if we can list buckets
    console.log('1️⃣ Testing bucket listing permissions...')
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()
    
    if (bucketsError) {
      console.error('❌ Failed to list buckets:', {
        message: bucketsError.message,
        details: bucketsError,
        status: bucketsError.status || 'unknown'
      })
      
      if (bucketsError.message.includes('row-level security policy') || bucketsError.message.includes('RLS')) {
        console.log('🚨 RLS POLICY ISSUE DETECTED!')
        console.log('The storage.buckets table has RLS enabled but no policies allow access.')
        console.log('This is the root cause of your image upload problem.')
      }
      return
    }
    
    console.log('✅ Successfully listed buckets:', buckets.map(b => b.name))
    
    // Test 2: Check if job-images bucket exists
    console.log('\n2️⃣ Checking for job-images bucket...')
    const jobImagesBucket = buckets.find(bucket => bucket.name === 'job-images')
    
    if (!jobImagesBucket) {
      console.log('⚠️ job-images bucket does not exist')
      
      // Test 3: Try to create the bucket
      console.log('\n3️⃣ Attempting to create job-images bucket...')
      const { error: createError } = await supabase.storage.createBucket('job-images', {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'],
        fileSizeLimit: 5242880 // 5MB
      })

      if (createError) {
        console.error('❌ Failed to create bucket:', {
          message: createError.message,
          details: createError,
          status: createError.status || 'unknown'
        })
        
        if (createError.message.includes('row-level security policy') || createError.message.includes('RLS')) {
          console.log('\n🚨 RLS POLICY ISSUE CONFIRMED!')
          console.log('The storage.buckets table has RLS enabled but no policies allow bucket creation.')
          console.log('\nSOLUTION NEEDED:')
          console.log('1. Go to Supabase Dashboard > SQL Editor')
          console.log('2. Run the RLS policy creation SQL commands')
          console.log('3. Or disable RLS on storage tables temporarily')
        }
        return
      }
      
      console.log('✅ Successfully created job-images bucket')
    } else {
      console.log('✅ job-images bucket already exists:', {
        public: jobImagesBucket.public,
        fileSizeLimit: jobImagesBucket.file_size_limit,
        allowedMimeTypes: jobImagesBucket.allowed_mime_types
      })
    }

    // Test 4: Try to upload a test file
    console.log('\n4️⃣ Testing file upload to job-images bucket...')
    const testContent = 'Test file for storage verification'
    const testFileName = `test-${Date.now()}.txt`
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('job-images')
      .upload(testFileName, testContent, {
        contentType: 'text/plain'
      })

    if (uploadError) {
      console.error('❌ Failed to upload test file:', {
        message: uploadError.message,
        details: uploadError,
        status: uploadError.status || 'unknown'
      })
      
      if (uploadError.message.includes('row-level security policy') || uploadError.message.includes('RLS')) {
        console.log('\n🚨 RLS POLICY ISSUE ON OBJECTS TABLE!')
        console.log('The storage.objects table has RLS enabled but no policies allow file uploads.')
        console.log('\nSOLUTION NEEDED:')
        console.log('1. Create RLS policies for storage.objects table')
        console.log('2. Allow authenticated users to insert/select/update/delete objects')
      }
      return
    }
    
    console.log('✅ Successfully uploaded test file:', uploadData.path)

    // Test 5: Try to get public URL
    console.log('\n5️⃣ Testing public URL generation...')
    const { data: urlData } = supabase.storage
      .from('job-images')
      .getPublicUrl(uploadData.path)
    
    console.log('✅ Generated public URL:', urlData.publicUrl)

    // Test 6: Clean up test file
    console.log('\n6️⃣ Cleaning up test file...')
    const { error: deleteError } = await supabase.storage
      .from('job-images')
      .remove([uploadData.path])

    if (deleteError) {
      console.error('⚠️ Failed to delete test file:', deleteError)
    } else {
      console.log('✅ Successfully deleted test file')
    }

    console.log('\n🎉 ALL TESTS PASSED!')
    console.log('Your storage setup is working correctly.')
    console.log('The image upload issue might be related to:')
    console.log('- Network connectivity')
    console.log('- File permissions on device')
    console.log('- Image file format/size issues')
    
  } catch (error) {
    console.error('💥 Unexpected error during diagnosis:', error)
  }
}

// Run the diagnosis
diagnosisStorageIssue()
