# Manual AVD creation script
# This creates the AVD configuration manually

Write-Host "Creating Android Virtual Device manually..." -ForegroundColor Green

# Set environment variables
$env:ANDROID_HOME = "D:\sdk"
$env:ANDROID_SDK_ROOT = "D:\sdk"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

# Create AVD directory
$avdDir = "$env:USERPROFILE\.android\avd"
$avdName = "Expo_Test_Device"
$avdPath = "$avdDir\$avdName.avd"

Write-Host "Creating AVD directory: $avdPath" -ForegroundColor Yellow

if (-not (Test-Path $avdDir)) {
    New-Item -ItemType Directory -Path $avdDir -Force | Out-Null
}

if (-not (Test-Path $avdPath)) {
    New-Item -ItemType Directory -Path $avdPath -Force | Out-Null
}

# Create config.ini
$configContent = @"
AvdId=$avdName
PlayStore.enabled=false
abi.type=x86_64
avd.ini.displayname=$avdName
avd.ini.encoding=UTF-8
disk.dataPartition.size=6442450944
fastboot.chosenSnapshotFile=
fastboot.forceChosenSnapshotBoot=no
fastboot.forceColdBoot=no
fastboot.forceFastBoot=yes
hw.accelerometer=yes
hw.arc=false
hw.audioInput=yes
hw.battery=yes
hw.camera.back=virtualscene
hw.camera.front=emulated
hw.cpu.arch=x86_64
hw.cpu.ncore=4
hw.dPad=no
hw.device.hash2=MD5:524e03c783a7f9c5a3c0107e0c5d9b2b
hw.device.manufacturer=Google
hw.device.name=Nexus 5X
hw.gps=yes
hw.gpu.enabled=yes
hw.gpu.mode=auto
hw.initialOrientation=Portrait
hw.keyboard=yes
hw.lcd.density=420
hw.lcd.height=2220
hw.lcd.width=1080
hw.mainKeys=no
hw.ramSize=2048
hw.sdCard=yes
hw.sensors.orientation=yes
hw.sensors.proximity=yes
hw.trackBall=no
image.sysdir.1=system-images\android-34\google_apis\x86_64\
runtime.network.latency=none
runtime.network.speed=full
sdcard.size=512M
showDeviceFrame=no
skin.dynamic=yes
skin.name=1080x2220
skin.path=_no_skin
tag.display=Google APIs
tag.id=google_apis
vm.heapSize=256
"@

$configPath = "$avdPath\config.ini"
$configContent | Out-File -FilePath $configPath -Encoding UTF8

Write-Host "Created config.ini at: $configPath" -ForegroundColor Green

# Create AVD ini file
$avdIniContent = @"
avd.ini.encoding=UTF-8
path=$avdPath
path.rel=avd\$avdName.avd
target=android-34
"@

$avdIniPath = "$avdDir\$avdName.ini"
$avdIniContent | Out-File -FilePath $avdIniPath -Encoding UTF8

Write-Host "Created AVD ini at: $avdIniPath" -ForegroundColor Green

Write-Host ""
Write-Host "AVD created successfully!" -ForegroundColor Green
Write-Host "You can now start the emulator with:" -ForegroundColor Yellow
Write-Host "emulator -avd $avdName" -ForegroundColor White
Write-Host ""
Write-Host "Or test with Expo Go by scanning the QR code in the Expo terminal!" -ForegroundColor Cyan
