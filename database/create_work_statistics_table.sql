-- Create work_statistics table for permanent completion history
-- This table stores a permanent record of completed work that persists even if the original job is deleted

CREATE TABLE IF NOT EXISTS work_statistics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  job_id UUID NOT NULL, -- Reference to original job (for tracking, but not a foreign key)
  installer_id VARCHAR(50) NOT NULL, -- installer1 or installer2
  customer_name VARCHAR(255) NOT NULL,
  place_name VARCHAR(255),
  area_name VARCHAR(255),
  completion_date TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS (Row Level Security) policies
ALTER TABLE work_statistics ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to read their own work statistics
CREATE POLICY "Users can read their own work statistics" ON work_statistics
  FOR SELECT TO authenticated 
  USING (true); -- We'll handle filtering in the application layer

-- Allow authenticated users to insert work statistics
CREATE POLICY "Allow authenticated users to insert work statistics" ON work_statistics
  FOR INSERT TO authenticated 
  WITH CHECK (true);

-- Prevent updates and deletes to maintain data integrity
CREATE POLICY "Prevent updates to work statistics" ON work_statistics
  FOR UPDATE TO authenticated 
  USING (false);

CREATE POLICY "Prevent deletes of work statistics" ON work_statistics
  FOR DELETE TO authenticated 
  USING (false);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_work_statistics_installer_id ON work_statistics(installer_id);
CREATE INDEX IF NOT EXISTS idx_work_statistics_completion_date ON work_statistics(completion_date);
CREATE INDEX IF NOT EXISTS idx_work_statistics_job_id ON work_statistics(job_id);
CREATE INDEX IF NOT EXISTS idx_work_statistics_installer_date ON work_statistics(installer_id, completion_date);

-- Create trigger function to automatically create work statistics when job is completed
CREATE OR REPLACE FUNCTION create_work_statistics_on_completion()
RETURNS TRIGGER AS $$
DECLARE
    place_name_val VARCHAR(255);
    area_name_val VARCHAR(255);
BEGIN
    -- Only create work statistics when completion_status changes to 'completed'
    IF NEW.completion_status = 'completed' AND 
       (OLD.completion_status IS NULL OR OLD.completion_status != 'completed') THEN
        
        -- Get place and area names
        SELECT p.name INTO place_name_val 
        FROM places p 
        WHERE p.id = NEW.place_id;
        
        SELECT a.name INTO area_name_val 
        FROM areas a 
        WHERE a.id = NEW.area_id;
        
        -- Insert into work_statistics table
        INSERT INTO work_statistics (
            job_id,
            installer_id,
            customer_name,
            place_name,
            area_name,
            completion_date
        ) VALUES (
            NEW.id,
            NEW.completed_by,
            NEW.customer_name,
            place_name_val,
            area_name_val,
            COALESCE(NEW.completed_at, NOW())
        );
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically populate work_statistics
DROP TRIGGER IF EXISTS trigger_create_work_statistics ON jobs;
CREATE TRIGGER trigger_create_work_statistics
    AFTER UPDATE ON jobs
    FOR EACH ROW
    EXECUTE FUNCTION create_work_statistics_on_completion();

-- Add comments for documentation
COMMENT ON TABLE work_statistics IS 'Permanent record of completed work that persists even if original job is deleted';
COMMENT ON COLUMN work_statistics.job_id IS 'Reference to original job ID (not a foreign key to allow job deletion)';
COMMENT ON COLUMN work_statistics.installer_id IS 'ID of installer who completed the work (installer1 or installer2)';
COMMENT ON COLUMN work_statistics.customer_name IS 'Customer name at time of completion';
COMMENT ON COLUMN work_statistics.place_name IS 'Place name at time of completion';
COMMENT ON COLUMN work_statistics.area_name IS 'Area name at time of completion';
COMMENT ON COLUMN work_statistics.completion_date IS 'Date and time when work was completed';

-- Migrate existing completed jobs to work_statistics table
INSERT INTO work_statistics (
    job_id,
    installer_id,
    customer_name,
    place_name,
    area_name,
    completion_date
)
SELECT 
    j.id,
    COALESCE(j.completed_by, j.assigned_installer) as installer_id,
    j.customer_name,
    p.name as place_name,
    a.name as area_name,
    COALESCE(j.completed_at, j.updated_at) as completion_date
FROM jobs j
LEFT JOIN places p ON j.place_id = p.id
LEFT JOIN areas a ON j.area_id = a.id
WHERE j.completion_status = 'completed'
AND NOT EXISTS (
    SELECT 1 FROM work_statistics ws 
    WHERE ws.job_id = j.id
);
