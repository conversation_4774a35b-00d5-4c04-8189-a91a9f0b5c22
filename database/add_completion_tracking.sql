-- Add completion tracking to jobs table
-- This migration adds a completed_at timestamp to track when installers mark jobs as completed

-- Add completed_at column to jobs table
ALTER TABLE jobs ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;

-- Add completed_by column to track which installer completed the job
ALTER TABLE jobs ADD COLUMN IF NOT EXISTS completed_by VARCHAR(50);

-- Create index for better performance on completion queries
CREATE INDEX IF NOT EXISTS idx_jobs_completed_at ON jobs(completed_at);
CREATE INDEX IF NOT EXISTS idx_jobs_completed_by ON jobs(completed_by);
CREATE INDEX IF NOT EXISTS idx_jobs_completion_status ON jobs(completion_status);

-- Create composite index for installer work statistics queries
CREATE INDEX IF NOT EXISTS idx_jobs_installer_completion ON jobs(assigned_installer, completion_status, completed_at);

-- Update existing completed jobs to have a completed_at timestamp (optional)
-- This sets completed_at to updated_at for existing completed jobs
UPDATE jobs 
SET completed_at = updated_at 
WHERE completion_status = 'completed' 
AND completed_at IS NULL;

-- Create a function to automatically set completed_at when completion_status changes to 'completed'
CREATE OR REPLACE FUNCTION set_completed_at()
RETURNS TRIGGER AS $$
BEGIN
    -- If completion_status is being set to 'completed' and completed_at is not set
    IF NEW.completion_status = 'completed' AND OLD.completion_status != 'completed' THEN
        NEW.completed_at = NOW();
    END IF;
    
    -- If completion_status is being changed from 'completed' to something else, clear completed_at
    IF OLD.completion_status = 'completed' AND NEW.completion_status != 'completed' THEN
        NEW.completed_at = NULL;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically set completed_at
DROP TRIGGER IF EXISTS trigger_set_completed_at ON jobs;
CREATE TRIGGER trigger_set_completed_at
    BEFORE UPDATE ON jobs
    FOR EACH ROW
    EXECUTE FUNCTION set_completed_at();

-- Add comments for documentation
COMMENT ON COLUMN jobs.completed_at IS 'Timestamp when the job was marked as completed by installer';
COMMENT ON COLUMN jobs.completed_by IS 'Role of the installer who completed the job (installer1 or installer2)';
