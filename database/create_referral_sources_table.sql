-- Create referral_sources table
CREATE TABLE IF NOT EXISTS referral_sources (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS (Row Level Security) policies
ALTER TABLE referral_sources ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to read all referral sources
CREATE POLICY "Allow authenticated users to read referral sources" ON referral_sources
  FOR SELECT TO authenticated USING (true);

-- Allow authenticated users to insert new referral sources
CREATE POLICY "Allow authenticated users to insert referral sources" ON referral_sources
  FOR INSERT TO authenticated WITH CHECK (true);

-- Allow authenticated users to update referral sources
CREATE POLICY "Allow authenticated users to update referral sources" ON referral_sources
  FOR UPDATE TO authenticated USING (true);

-- Allow authenticated users to delete referral sources
CREATE POLICY "Allow authenticated users to delete referral sources" ON referral_sources
  FOR DELETE TO authenticated USING (true);

-- Add referral_source_id column to jobs table
ALTER TABLE jobs ADD COLUMN IF NOT EXISTS referral_source_id UUID REFERENCES referral_sources(id);

-- Insert some default referral sources
INSERT INTO referral_sources (name) VALUES 
  ('هاوڕێ'),
  ('خێزان'),
  ('ڕێکلام لە سۆشیال میدیا'),
  ('گووگڵ'),
  ('کڕیاری پێشوو'),
  ('کارمەند'),
  ('ڕێکلامی ڕادیۆ/تەلەڤیزیۆن'),
  ('بەڵگەنامە'),
  ('ماڵپەڕی ئینتەرنێت'),
  ('هیتر')
ON CONFLICT (name) DO NOTHING;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_referral_sources_name ON referral_sources(name);
CREATE INDEX IF NOT EXISTS idx_jobs_referral_source_id ON jobs(referral_source_id);

-- Update trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_referral_sources_updated_at 
  BEFORE UPDATE ON referral_sources 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
