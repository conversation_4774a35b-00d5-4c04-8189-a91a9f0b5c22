# PowerShell script to set up Android SDK environment variables
# Run this script as Administrator

Write-Host "Android SDK Environment Setup Script" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Common Android SDK locations
$possiblePaths = @(
    "D:\sdk",
    "$env:USERPROFILE\AppData\Local\Android\Sdk",
    "$env:LOCALAPPDATA\Android\Sdk",
    "C:\Android\sdk",
    "C:\Program Files\Android\Android Studio\sdk",
    "C:\Users\<USER>\AppData\Local\Android\Sdk"
)

Write-Host ""
Write-Host "Searching for Android SDK..." -ForegroundColor Yellow

$androidHome = $null
foreach ($path in $possiblePaths) {
    if (Test-Path $path) {
        Write-Host "Found Android SDK at: $path" -ForegroundColor Green
        $androidHome = $path
        break
    }
}

if (-not $androidHome) {
    Write-Host "Android SDK not found in common locations." -ForegroundColor Red
    Write-Host "Please install Android Studio or Android SDK first." -ForegroundColor Yellow
    Write-Host "Common installation steps:" -ForegroundColor Yellow
    Write-Host "1. Download Android Studio from https://developer.android.com/studio" -ForegroundColor White
    Write-Host "2. Install Android Studio" -ForegroundColor White
    Write-Host "3. Open Android Studio and install SDK through SDK Manager" -ForegroundColor White
    Write-Host "4. Run this script again" -ForegroundColor White
    exit 1
}

Write-Host ""
Write-Host "Setting up environment variables..." -ForegroundColor Yellow

try {
    # Set ANDROID_HOME
    [Environment]::SetEnvironmentVariable("ANDROID_HOME", $androidHome, "User")
    Write-Host "Set ANDROID_HOME = $androidHome" -ForegroundColor Green

    # Get current PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

    # Add Android SDK tools to PATH if not already present
    $platformTools = "$androidHome\platform-tools"
    $tools = "$androidHome\tools"
    $toolsBin = "$androidHome\tools\bin"

    $pathsToAdd = @($platformTools, $tools, $toolsBin)
    $pathUpdated = $false

    foreach ($pathToAdd in $pathsToAdd) {
        if ($currentPath -notlike "*$pathToAdd*") {
            $currentPath = "$currentPath;$pathToAdd"
            $pathUpdated = $true
            Write-Host "Added to PATH: $pathToAdd" -ForegroundColor Green
        } else {
            Write-Host "Already in PATH: $pathToAdd" -ForegroundColor Blue
        }
    }

    if ($pathUpdated) {
        [Environment]::SetEnvironmentVariable("PATH", $currentPath, "User")
        Write-Host "Updated PATH environment variable" -ForegroundColor Green
    }

    Write-Host ""
    Write-Host "Environment variables set successfully!" -ForegroundColor Green
    Write-Host "Please restart your terminal/PowerShell for changes to take effect." -ForegroundColor Yellow

    Write-Host ""
    Write-Host "Summary:" -ForegroundColor Cyan
    Write-Host "ANDROID_HOME = $androidHome" -ForegroundColor White
    Write-Host "Added to PATH:" -ForegroundColor White
    foreach ($pathToAdd in $pathsToAdd) {
        Write-Host "  - $pathToAdd" -ForegroundColor White
    }

    Write-Host ""
    Write-Host "To test the setup after restarting terminal:" -ForegroundColor Cyan
    Write-Host "adb version" -ForegroundColor White
    Write-Host "echo `$env:ANDROID_HOME" -ForegroundColor White

} catch {
    Write-Host "Error setting environment variables: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please run this script as Administrator" -ForegroundColor Yellow
}
