/**
 * Test Job Images - Check if jobs have images stored
 */

const { createClient } = require('@supabase/supabase-js')

// Your Supabase configuration
const SUPABASE_URL = 'https://emxbbmsieclwlslwgkua.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVteGJibXNpZWNsd2xzbHdna3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ3MzgsImV4cCI6MjA2MzkzMDczOH0.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

async function testJobImages() {
  console.log('🖼️ Testing Job Images...\n')

  try {
    // Get recent jobs to check for images
    console.log('1️⃣ Fetching recent jobs...')
    
    const { data: jobs, error: jobsError } = await supabase
      .from('jobs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10)

    if (jobsError) {
      console.error('❌ Failed to fetch jobs:', jobsError.message)
      return
    }

    console.log(`✅ Found ${jobs.length} recent jobs`)

    // Check each job for images
    console.log('\n2️⃣ Checking jobs for images...')
    
    let jobsWithImages = 0
    let totalImages = 0

    jobs.forEach((job, index) => {
      console.log(`\nJob ${index + 1}: ${job.customer_name}`)
      console.log(`  ID: ${job.id}`)
      console.log(`  Date: ${job.date}`)
      
      if (job.images && job.images.length > 0) {
        jobsWithImages++
        totalImages += job.images.length
        console.log(`  ✅ Has ${job.images.length} images:`)
        job.images.forEach((image, imgIndex) => {
          console.log(`    ${imgIndex + 1}. ${image}`)
        })
      } else {
        console.log(`  ⚪ No images`)
      }
    })

    console.log(`\n📊 Summary:`)
    console.log(`  Total jobs checked: ${jobs.length}`)
    console.log(`  Jobs with images: ${jobsWithImages}`)
    console.log(`  Total images: ${totalImages}`)

    // Test image accessibility
    if (totalImages > 0) {
      console.log('\n3️⃣ Testing image accessibility...')
      
      const jobWithImages = jobs.find(job => job.images && job.images.length > 0)
      if (jobWithImages && jobWithImages.images.length > 0) {
        const testImageUrl = jobWithImages.images[0]
        console.log(`Testing image: ${testImageUrl}`)
        
        try {
          const response = await fetch(testImageUrl)
          if (response.ok) {
            console.log('✅ Image is accessible')
            console.log(`  Status: ${response.status}`)
            console.log(`  Content-Type: ${response.headers.get('content-type')}`)
            console.log(`  Size: ${response.headers.get('content-length')} bytes`)
          } else {
            console.log(`❌ Image not accessible: ${response.status} ${response.statusText}`)
          }
        } catch (fetchError) {
          console.log(`❌ Failed to fetch image: ${fetchError.message}`)
        }
      }
    }

    // Check storage bucket
    console.log('\n4️⃣ Checking storage bucket...')
    
    const { data: files, error: listError } = await supabase.storage
      .from('job-images')
      .list()

    if (listError) {
      console.log('❌ Cannot access storage bucket:', listError.message)
    } else {
      console.log(`✅ Storage bucket accessible`)
      console.log(`  Files in bucket: ${files.length}`)
      
      if (files.length > 0) {
        console.log('  Recent files:')
        files.slice(0, 5).forEach((file, index) => {
          console.log(`    ${index + 1}. ${file.name} (${file.metadata?.size || 'unknown'} bytes)`)
        })
      }
    }

    console.log('\n🎯 RECOMMENDATIONS:')
    
    if (jobsWithImages === 0) {
      console.log('❌ No jobs have images stored')
      console.log('💡 Try adding a job with images to test the functionality')
    } else {
      console.log('✅ Jobs with images found')
      console.log('💡 Images should be visible in your app job cards')
      console.log('💡 Tap on image thumbnails to view them full-screen')
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }
}

// Run the test
testJobImages()
