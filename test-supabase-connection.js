/**
 * Comprehensive Supabase Connection Test
 * This will help identify why you can't connect to your Supabase account
 */

const { createClient } = require('@supabase/supabase-js')

// Your Supabase configuration from .env
const SUPABASE_URL = 'https://emxbbmsieclwlslwgkua.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs'

console.log('🔍 Testing Supabase Connection...\n')

async function testSupabaseConnection() {
  try {
    // Test 1: Basic URL and Key validation
    console.log('1️⃣ Validating configuration...')
    console.log(`URL: ${SUPABASE_URL}`)
    console.log(`Key: ${SUPABASE_ANON_KEY.substring(0, 20)}...`)
    
    if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
      console.error('❌ Missing Supabase URL or Key')
      return
    }
    
    if (!SUPABASE_URL.includes('supabase.co')) {
      console.error('❌ Invalid Supabase URL format')
      return
    }
    
    console.log('✅ Configuration looks valid')

    // Test 2: Create Supabase client
    console.log('\n2️⃣ Creating Supabase client...')
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)
    console.log('✅ Supabase client created successfully')

    // Test 3: Test basic connectivity
    console.log('\n3️⃣ Testing basic connectivity...')
    
    try {
      // Try to make a simple request to test connectivity
      const { data, error } = await supabase.from('users').select('count').limit(1)
      
      if (error) {
        console.log(`⚠️ Database query failed: ${error.message}`)
        
        // Check for common error types
        if (error.message.includes('relation "users" does not exist')) {
          console.log('ℹ️ This is normal - users table might not exist yet')
        } else if (error.message.includes('JWT')) {
          console.error('❌ Authentication token issue')
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          console.error('❌ Network connectivity issue')
        } else {
          console.log('ℹ️ Connection established but query failed (this might be normal)')
        }
      } else {
        console.log('✅ Database connection successful')
      }
    } catch (networkError) {
      console.error('❌ Network error:', networkError.message)
      
      if (networkError.message.includes('fetch')) {
        console.log('\n🌐 NETWORK ISSUE DETECTED:')
        console.log('- Check your internet connection')
        console.log('- Check if your firewall is blocking the connection')
        console.log('- Try accessing the Supabase URL in your browser')
      }
    }

    // Test 4: Test authentication
    console.log('\n4️⃣ Testing authentication...')
    
    try {
      const { data: authData, error: authError } = await supabase.auth.getSession()
      
      if (authError) {
        console.log(`⚠️ Auth check failed: ${authError.message}`)
      } else {
        console.log('✅ Authentication system accessible')
        console.log(`Current session: ${authData.session ? 'Active' : 'None'}`)
      }
    } catch (authErr) {
      console.error('❌ Authentication test failed:', authErr.message)
    }

    // Test 5: Test storage access
    console.log('\n5️⃣ Testing storage access...')
    
    try {
      const { data: buckets, error: storageError } = await supabase.storage.listBuckets()
      
      if (storageError) {
        console.log(`⚠️ Storage access failed: ${storageError.message}`)
        
        if (storageError.message.includes('row-level security')) {
          console.log('ℹ️ This is the RLS issue we identified earlier')
        }
      } else {
        console.log('✅ Storage system accessible')
        console.log(`Buckets found: ${buckets.length}`)
      }
    } catch (storageErr) {
      console.error('❌ Storage test failed:', storageErr.message)
    }

    // Test 6: Test specific tables your app uses
    console.log('\n6️⃣ Testing app-specific tables...')
    
    const tables = ['jobs', 'places', 'areas', 'referral_sources']
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('count').limit(1)
        
        if (error) {
          console.log(`⚠️ Table '${table}': ${error.message}`)
        } else {
          console.log(`✅ Table '${table}': Accessible`)
        }
      } catch (tableErr) {
        console.log(`❌ Table '${table}': ${tableErr.message}`)
      }
    }

    // Test 7: Check project status
    console.log('\n7️⃣ Checking project status...')
    
    try {
      // Try to access a system table to check if project is active
      const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
        headers: {
          'apikey': SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`
        }
      })
      
      if (response.ok) {
        console.log('✅ Project is active and accessible')
      } else {
        console.error(`❌ Project access failed: ${response.status} ${response.statusText}`)
        
        if (response.status === 401) {
          console.log('🔑 AUTHENTICATION ISSUE:')
          console.log('- Your API key might be invalid')
          console.log('- Check if you copied the correct anon key from Supabase Dashboard')
        } else if (response.status === 404) {
          console.log('🏗️ PROJECT ISSUE:')
          console.log('- Your project might be paused or deleted')
          console.log('- Check your Supabase Dashboard')
        }
      }
    } catch (fetchErr) {
      console.error('❌ Project status check failed:', fetchErr.message)
    }

    console.log('\n📋 SUMMARY:')
    console.log('If you see mostly ✅ marks above, your connection is working.')
    console.log('If you see ❌ marks, those indicate the specific issues to fix.')
    
  } catch (error) {
    console.error('💥 Unexpected error during connection test:', error)
  }
}

// Run the connection test
testSupabaseConnection()
