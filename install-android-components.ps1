# Script to install Android SDK components and create virtual device
# Run this AFTER restarting your terminal

Write-Host "Installing Android SDK Components" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Check if sdkmanager is available
try {
    $sdkManagerTest = & .\run-sdkmanager.bat --version 2>&1
    Write-Host "SDK Manager found: $sdkManagerTest" -ForegroundColor Green
} catch {
    Write-Host "Error: sdkmanager not found" -ForegroundColor Red
    Write-Host "Please check if run-sdkmanager.bat exists" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Installing required SDK components..." -ForegroundColor Yellow

# Accept licenses first
Write-Host "Accepting SDK licenses..." -ForegroundColor Yellow
echo "y" | .\run-sdkmanager.bat --licenses

# Install essential components
Write-Host "Installing platform-tools..." -ForegroundColor Cyan
.\run-sdkmanager.bat "platform-tools"

Write-Host "Installing build-tools..." -ForegroundColor Cyan
.\run-sdkmanager.bat "build-tools;34.0.0"

Write-Host "Installing Android platform..." -ForegroundColor Cyan
.\run-sdkmanager.bat "platforms;android-34"

Write-Host "Installing emulator..." -ForegroundColor Cyan
.\run-sdkmanager.bat "emulator"

Write-Host "Installing system image..." -ForegroundColor Cyan
.\run-sdkmanager.bat "system-images;android-34;google_apis;x86_64"

Write-Host ""
Write-Host "Creating Android Virtual Device..." -ForegroundColor Yellow

# Create AVD
$avdName = "Expo_Test_Device"
$systemImage = "system-images;android-34;google_apis;x86_64"

Write-Host "Creating AVD: $avdName" -ForegroundColor Cyan
echo "no" | .\run-avdmanager.bat create avd -n $avdName -k $systemImage -f

Write-Host ""
Write-Host "Setup Complete!" -ForegroundColor Green
Write-Host "===============" -ForegroundColor Green
Write-Host ""
Write-Host "Available commands:" -ForegroundColor Cyan
Write-Host "List AVDs: emulator -list-avds" -ForegroundColor White
Write-Host "Start emulator: emulator -avd $avdName" -ForegroundColor White
Write-Host "Check devices: adb devices" -ForegroundColor White
Write-Host "Run Expo app: npx expo run:android" -ForegroundColor White
Write-Host ""
Write-Host "To start the emulator and run your app:" -ForegroundColor Yellow
Write-Host "1. emulator -avd $avdName" -ForegroundColor White
Write-Host "2. Wait for emulator to boot completely" -ForegroundColor White
Write-Host "3. npx expo run:android" -ForegroundColor White
