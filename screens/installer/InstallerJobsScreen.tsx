import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
  RefreshControl,
  Modal,
  Image,
  Linking,
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { Picker } from '@react-native-picker/picker'
import { supabase } from '../../lib/supabase'
import { useAuth } from '../../contexts/AuthContext'
import WorkingImage from '../../components/WorkingImage'
import FullScreenImageViewer from '../../components/FullScreenImageViewer'

interface Job {
  id: string
  date: string
  customer_name: string
  customer_phone: string
  place_id?: string
  area_id?: string
  referral_source_id?: string
  place_name?: string
  area_name?: string
  referral_source_name?: string
  completion_status: string
  images?: string[]
  latitude?: number
  longitude?: number
  created_at: string
}

const InstallerJobsScreen = () => {
  const [jobs, setJobs] = useState<Job[]>([])
  const [filteredJobs, setFilteredJobs] = useState<Job[]>([])
  const [loading, setLoading] = useState(true)
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [fullScreenImageVisible, setFullScreenImageVisible] = useState(false)
  const { user, logout } = useAuth()

  useEffect(() => {
    fetchJobs()
  }, [])

  useEffect(() => {
    filterJobs()
  }, [jobs, searchText, statusFilter])

  const fetchJobs = async () => {
    try {
      const installerRole = user?.role === 'installer1' ? 'installer1' : 'installer2'
      
      const { data, error } = await supabase
        .from('jobs')
        .select(`
          *,
          places(name),
          areas(name),
          referral_sources(name)
        `)
        .eq('assigned_installer', installerRole)
        .eq('job_status', 'ready')
        .eq('archived_by_installer', false)
        .order('created_at', { ascending: false })

      if (error) throw error

      const formattedJobs = data?.map(job => ({
        ...job,
        place_id: job.place_id,
        area_id: job.area_id,
        referral_source_id: job.referral_source_id,
        place_name: job.places?.name,
        area_name: job.areas?.name,
        referral_source_name: job.referral_sources?.name,
      })) || []

      setJobs(formattedJobs)
    } catch (error) {
      console.error('Error fetching jobs:', error)
      Alert.alert('هەڵە', 'نەتوانرا کارەکان بهێنرێنەوە')
    } finally {
      setLoading(false)
    }
  }

  const filterJobs = () => {
    let filtered = jobs

    // Search filter
    if (searchText) {
      filtered = filtered.filter(job => 
        job.customer_name.toLowerCase().includes(searchText.toLowerCase()) ||
        job.customer_phone.includes(searchText)
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(job => job.completion_status === statusFilter)
    }

    setFilteredJobs(filtered)
  }

  const updateJobStatus = async (jobId: string, status: string) => {
    try {
      const installerRole = user?.role === 'installer1' ? 'installer1' : 'installer2'

      console.log('InstallerJobsScreen: Updating job status', { jobId, status, installerRole })

      const updateData: any = {
        completion_status: status,
        updated_at: new Date().toISOString()
      }

      // If marking as completed, add completion tracking data
      if (status === 'completed') {
        updateData.completed_at = new Date().toISOString()
        updateData.completed_by = installerRole
        console.log('InstallerJobsScreen: Adding completion tracking data', updateData)
      }

      const { error } = await supabase
        .from('jobs')
        .update(updateData)
        .eq('id', jobId)

      if (error) {
        console.error('InstallerJobsScreen: Supabase update error:', error)
        throw error
      }

      console.log('InstallerJobsScreen: Job status updated successfully')
      Alert.alert('سەرکەوتوو', 'کارەکە وەک تەواو نیشان کرا')
      fetchJobs()
    } catch (error) {
      console.error('Error updating job status:', error)
      Alert.alert('هەڵە', 'نەتوانرا دۆخی کارەکە نوێ بکرێتەوە')
    }
  }



  const openMap = (latitude?: number, longitude?: number) => {
    if (!latitude || !longitude) {
      Alert.alert('هەڵە', 'شوێنی ئەم کارە دیاری نەکراوە')
      return
    }

    const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`
    Linking.openURL(url).catch(() => {
      Alert.alert('هەڵە', 'نەتوانرا نەخشەکە بکرێتەوە')
    })
  }

  const callCustomer = (phoneNumber: string) => {
    const url = `tel:${phoneNumber}`
    Linking.openURL(url).catch(() => {
      Alert.alert('هەڵە', 'نەتوانرا پەیوەندی بکرێت')
    })
  }

  const renderJobCard = ({ item }: { item: Job }) => (
    <View style={[
      styles.jobCard,
      item.completion_status === 'completed' ? styles.completedCard : styles.pendingCard
    ]}>
      {/* Header with customer name and status badge */}
      <View style={styles.jobHeader}>
        <View style={styles.customerInfo}>
          <View style={styles.customerNameRow}>
            <Ionicons name="person" size={18} color="#333" />
            <Text style={styles.customerName}>{item.customer_name}</Text>
          </View>
        </View>
        <View style={[
          styles.statusBadge,
          item.completion_status === 'completed' ? styles.completedBadge : styles.pendingBadge
        ]}>
          <Ionicons
            name={item.completion_status === 'completed' ? 'checkmark-circle' : 'time'}
            size={14}
            color="white"
          />
          <Text style={styles.statusBadgeText}>
            {item.completion_status === 'completed' ? 'تەواو' : 'چاوەڕوانە'}
          </Text>
        </View>
      </View>

      {/* Contact and location info */}
      <View style={styles.infoSection}>
        <TouchableOpacity
          style={styles.infoRow}
          onPress={() => callCustomer(item.customer_phone)}
        >
          <Ionicons name="call" size={16} color="#007AFF" />
          <Text style={styles.customerPhone}>{item.customer_phone}</Text>
        </TouchableOpacity>

        {/* Referral Source */}
        {item.referral_source_name && (
          <View style={styles.infoRow}>
            <Ionicons name="people" size={16} color="#17a2b8" />
            <Text style={styles.referralSource}>
              لە لایەن : {item.referral_source_name}
            </Text>
          </View>
        )}

        <View style={styles.infoRow}>
          <Ionicons name="location" size={16} color="#FF6B35" />
          <Text style={styles.location}>
            {item.place_name} - {item.area_name}
          </Text>
        </View>

        <View style={styles.infoRow}>
          <Ionicons name="calendar" size={16} color="#6C757D" />
          <Text style={styles.jobDate}>
            {new Date(item.date).toLocaleDateString('ku')}
          </Text>
        </View>
      </View>

      {/* Images section */}
      {item.images && item.images.length > 0 && (
        <View style={styles.imagesContainer}>
          <View style={styles.imagesHeader}>
            <Ionicons name="images" size={16} color="#6C757D" />
            <Text style={styles.imagesLabel}>{item.images.length} وێنە</Text>
          </View>
          <View style={styles.imagesList}>
            {item.images.slice(0, 3).map((image, index) => (
              <WorkingImage
                key={index}
                source={{ uri: image }}
                style={styles.thumbImage}
                containerStyle={styles.imageThumb}
                onPress={() => {
                  setSelectedImage(image)
                  setFullScreenImageVisible(true)
                }}
              />
            ))}
            {item.images.length > 3 && (
              <View style={styles.moreImagesIndicator}>
                <Text style={styles.moreImagesText}>+{item.images.length - 3}</Text>
              </View>
            )}
          </View>
        </View>
      )}

      {/* Action buttons */}
      <View style={styles.actionButtons}>
        {item.completion_status === 'pending' && (
          <TouchableOpacity
            style={[styles.actionButton, styles.completedButton]}
            onPress={() => updateJobStatus(item.id, 'completed')}
          >
            <Text style={styles.buttonText}>تەواو</Text>
            <Ionicons name="checkmark-circle" size={16} color="white" />
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.actionButton, styles.mapButton]}
          onPress={() => openMap(item.latitude, item.longitude)}
        >
          <Text style={styles.buttonText}>نەخشە</Text>
          <Ionicons name="map-outline" size={16} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  )

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={logout} style={styles.logoutButton}>
          <Ionicons name="log-out-outline" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>کارەکانم</Text>
        <Text style={styles.welcomeText}>بەخێربێیت {user?.display_name}</Text>
      </View>

      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="گەڕان بە ناو یان ژمارەی تەلەفۆن"
          value={searchText}
          onChangeText={setSearchText}
          textAlign="right"
        />
      </View>

      <View style={styles.filtersContainer}>
        <View style={styles.filterItem}>
          <Text style={styles.filterLabel}>دۆخ:</Text>
          <Picker
            selectedValue={statusFilter}
            onValueChange={setStatusFilter}
            style={styles.picker}
          >
            <Picker.Item label="هەموو" value="all" />
            <Picker.Item label="چاوەڕوانە" value="pending" />
            <Picker.Item label="تەواو بووە" value="completed" />
          </Picker>
        </View>
      </View>

      <FlatList
        data={filteredJobs}
        renderItem={renderJobCard}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={fetchJobs} />
        }
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="briefcase-outline" size={64} color="#ccc" />
            <Text style={styles.emptyText}>هیچ کارێک نەدۆزرایەوە</Text>
          </View>
        }
      />

      {/* Full Screen Image Viewer */}
      <FullScreenImageViewer
        visible={fullScreenImageVisible}
        imageUri={selectedImage || ''}
        onClose={() => {
          setFullScreenImageVisible(false)
          setSelectedImage(null)
        }}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: 'white',
    padding: 16,
    paddingTop: 50,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  welcomeText: {
    fontSize: 14,
    color: '#666',
  },
  logoutButton: {
    padding: 8,
  },
  searchContainer: {
    padding: 16,
  },
  searchInput: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    textAlign: 'right',
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  filterItem: {
    flex: 1,
    marginRight: 8,
  },
  filterLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
    textAlign: 'right',
  },
  picker: {
    backgroundColor: 'white',
    borderRadius: 8,
  },
  listContainer: {
    padding: 16,
  },
  jobCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 8,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  completedCard: {
    borderRightWidth: 4,
    borderRightColor: '#28a745',
  },
  pendingCard: {
    borderRightWidth: 4,
    borderRightColor: '#ffc107',
  },
  jobHeader: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  customerInfo: {
    flex: 1,
  },
  customerNameRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginBottom: 4,
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
    flex: 1,
    textAlign: 'right',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 8,
  },
  completedBadge: {
    backgroundColor: '#28a745',
  },
  pendingBadge: {
    backgroundColor: '#ffc107',
  },
  statusBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  infoSection: {
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginBottom: 8,
  },
  customerPhone: {
    fontSize: 16,
    color: '#007AFF',
    marginRight: 8,
    fontWeight: '500',
    textAlign: 'right',
    flex: 1,
  },
  location: {
    fontSize: 14,
    color: '#FF6B35',
    marginRight: 8,
    fontWeight: '500',
    textAlign: 'right',
    flex: 1,
  },
  referralSource: {
    fontSize: 14,
    color: '#17a2b8',
    marginRight: 8,
    fontWeight: '500',
    textAlign: 'right',
    flex: 1,
  },
  jobDate: {
    fontSize: 14,
    color: '#6C757D',
    marginRight: 8,
    textAlign: 'right',
    flex: 1,
  },
  imagesContainer: {
    marginBottom: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 12,
  },
  imagesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  imagesLabel: {
    fontSize: 14,
    color: '#6C757D',
    marginLeft: 6,
    fontWeight: '500',
  },
  imagesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  imageThumb: {
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  thumbImage: {
    width: 70,
    height: 70,
  },
  moreImagesIndicator: {
    width: 70,
    height: 70,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreImagesText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
    marginTop: 4,
  },
  actionButton: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderRadius: 8,
    marginRight: 6,
    marginBottom: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
    minWidth: 44,
    justifyContent: 'center',
  },
  mapButton: {
    backgroundColor: '#007AFF',
    flex: 1,
    maxWidth: 80,
  },
  completedButton: {
    backgroundColor: '#28a745',
    flex: 1,
    maxWidth: 80,
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginRight: 4,
    textAlign: 'right',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },

})

export default InstallerJobsScreen
