import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { Picker } from '@react-native-picker/picker'
import { supabase } from '../../lib/supabase'
import { useAuth } from '../../contexts/AuthContext'

interface CompletedWork {
  id: string
  customer_name: string
  completion_date: string
  place_name?: string
  area_name?: string
}

const WorkStatsScreen = () => {
  const [completedWork, setCompletedWork] = useState<CompletedWork[]>([])
  const [filteredWork, setFilteredWork] = useState<CompletedWork[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedMonth, setSelectedMonth] = useState<string>('')
  const [selectedYear, setSelectedYear] = useState<string>('')
  const { user, logout } = useAuth()

  // Generate year options (current year and previous 2 years)
  const currentYear = new Date().getFullYear()
  const yearOptions = [currentYear, currentYear - 1, currentYear - 2]

  // Kurdish month names
  const monthNames = [
    'کانوونی دووەم', 'شوبات', 'ئازار', 'نیسان', 'ئایار', 'حوزەیران',
    'تەمووز', 'ئاب', 'ئەیلوول', 'تشرینی یەکەم', 'تشرینی دووەم', 'کانوونی یەکەم'
  ]

  useEffect(() => {
    fetchCompletedWork()
  }, [])

  useEffect(() => {
    filterWork()
  }, [completedWork, selectedMonth, selectedYear])

  const fetchCompletedWork = async () => {
    try {
      setLoading(true)
      const installerRole = user?.role === 'installer1' ? 'installer1' : 'installer2'

      const { data, error } = await supabase
        .from('work_statistics')
        .select('*')
        .eq('installer_id', installerRole)
        .order('completion_date', { ascending: false })

      if (error) throw error

      setCompletedWork(data || [])
    } catch (error) {
      console.error('Error fetching completed work:', error)
      Alert.alert('هەڵە', 'نەتوانرا ئاماری کارکردن بهێنرێتەوە')
    } finally {
      setLoading(false)
    }
  }

  const filterWork = () => {
    let filtered = completedWork

    if (selectedMonth && selectedYear) {
      filtered = filtered.filter(work => {
        const completedDate = new Date(work.completion_date)
        const workMonth = completedDate.getMonth() + 1
        const workYear = completedDate.getFullYear()

        return workMonth === parseInt(selectedMonth) && workYear === parseInt(selectedYear)
      })
    }

    setFilteredWork(filtered)
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const day = date.getDate()
    const month = date.getMonth()
    const year = date.getFullYear()
    
    return `${day} ${monthNames[month]} ${year}`
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }

  const clearFilters = () => {
    setSelectedMonth('')
    setSelectedYear('')
  }

  const renderWorkItem = ({ item }: { item: CompletedWork }) => (
    <View style={styles.workCard}>
      <View style={styles.workHeader}>
        <View style={styles.customerInfo}>
          <View style={styles.customerNameRow}>
            <Ionicons name="person" size={18} color="#333" />
            <Text style={styles.customerName}>{item.customer_name}</Text>
          </View>
          {item.place_name && (
            <View style={styles.locationRow}>
              <Ionicons name="location" size={14} color="#FF6B35" />
              <Text style={styles.locationText}>
                {item.place_name}{item.area_name ? ` - ${item.area_name}` : ''}
              </Text>
            </View>
          )}
        </View>
        <View style={styles.completedBadge}>
          <Ionicons name="checkmark-circle" size={14} color="white" />
          <Text style={styles.badgeText}>تەواو</Text>
        </View>
      </View>
      
      <View style={styles.completionInfo}>
        <View style={styles.dateTimeRow}>
          <Ionicons name="calendar" size={16} color="#007AFF" />
          <Text style={styles.dateText}>{formatDate(item.completion_date)}</Text>
        </View>
        <View style={styles.dateTimeRow}>
          <Ionicons name="time" size={16} color="#6C757D" />
          <Text style={styles.timeText}>{formatTime(item.completion_date)}</Text>
        </View>
      </View>
    </View>
  )

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>ئاماری کارکردن</Text>
          <TouchableOpacity onPress={logout} style={styles.logoutButton}>
            <Ionicons name="log-out-outline" size={24} color="#FF3B30" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <Text style={styles.filtersTitle}>پاڵاوتن بە مانگ و ساڵ:</Text>
        
        <View style={styles.filterRow}>
          <View style={styles.filterItem}>
            <Text style={styles.filterLabel}>مانگ:</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={selectedMonth}
                onValueChange={setSelectedMonth}
                style={styles.picker}
              >
                <Picker.Item label="هەموو مانگەکان" value="" />
                {monthNames.map((month, index) => (
                  <Picker.Item key={index + 1} label={month} value={(index + 1).toString()} />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.filterItem}>
            <Text style={styles.filterLabel}>ساڵ:</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={selectedYear}
                onValueChange={setSelectedYear}
                style={styles.picker}
              >
                <Picker.Item label="هەموو ساڵەکان" value="" />
                {yearOptions.map(year => (
                  <Picker.Item key={year} label={year.toString()} value={year.toString()} />
                ))}
              </Picker>
            </View>
          </View>
        </View>

        {(selectedMonth || selectedYear) && (
          <TouchableOpacity style={styles.clearButton} onPress={clearFilters}>
            <Ionicons name="refresh" size={16} color="white" />
            <Text style={styles.clearButtonText}>پاککردنەوەی پاڵاوتن</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Summary */}
      <View style={styles.summaryContainer}>
        <View style={styles.summaryCard}>
          <Ionicons name="briefcase" size={24} color="#007AFF" />
          <Text style={styles.summaryNumber}>{filteredWork.length}</Text>
          <Text style={styles.summaryLabel}>
            {selectedMonth && selectedYear 
              ? `کاری تەواوکراو لە ${monthNames[parseInt(selectedMonth) - 1]} ${selectedYear}`
              : 'کۆی کاری تەواوکراو'
            }
          </Text>
        </View>
      </View>

      {/* Work List */}
      <FlatList
        data={filteredWork}
        renderItem={renderWorkItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={fetchCompletedWork} />
        }
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="briefcase-outline" size={64} color="#ccc" />
            <Text style={styles.emptyText}>
              {selectedMonth && selectedYear 
                ? `هیچ کارێک لە ${monthNames[parseInt(selectedMonth) - 1]} ${selectedYear} تەواو نەکراوە`
                : 'هیچ کارێک تەواو نەکراوە'
              }
            </Text>
          </View>
        }
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#007AFF',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'right',
  },
  logoutButton: {
    padding: 8,
  },
  filtersContainer: {
    backgroundColor: 'white',
    margin: 16,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  filtersTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
    textAlign: 'right',
  },
  filterRow: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
  },
  filterItem: {
    flex: 1,
    marginHorizontal: 4,
  },
  filterLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    textAlign: 'right',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#f8f9fa',
  },
  picker: {
    height: 50,
  },
  clearButton: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6C757D',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginTop: 12,
  },
  clearButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginRight: 6,
  },
  summaryContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  summaryCard: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#007AFF',
    marginVertical: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  workCard: {
    backgroundColor: 'white',
    marginBottom: 12,
    borderRadius: 12,
    padding: 16,
    borderRightWidth: 4,
    borderRightColor: '#28a745',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  workHeader: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  customerInfo: {
    flex: 1,
  },
  customerNameRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginBottom: 4,
  },
  customerName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 8,
    textAlign: 'right',
  },
  locationRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 14,
    color: '#FF6B35',
    marginRight: 6,
    textAlign: 'right',
  },
  completedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#28a745',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  completionInfo: {
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    paddingTop: 12,
  },
  dateTimeRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginBottom: 4,
  },
  dateText: {
    fontSize: 14,
    color: '#007AFF',
    marginRight: 8,
    fontWeight: '500',
    textAlign: 'right',
  },
  timeText: {
    fontSize: 14,
    color: '#6C757D',
    marginRight: 8,
    textAlign: 'right',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
})

export default WorkStatsScreen
