import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native'
import { Picker } from '@react-native-picker/picker'
import { Ionicons } from '@expo/vector-icons'
import { supabase } from '../../lib/supabase'
import { useAuth } from '../../contexts/AuthContext'

interface MonthlyStats {
  year: number
  month: number
  totalJobs: number
  completedJobs: number
  notCompletedJobs: number
  pendingJobs: number
}

const InstallerStatsScreen = () => {
  const [stats, setStats] = useState<MonthlyStats[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString())
  const [selectedMonth, setSelectedMonth] = useState((new Date().getMonth() + 1).toString())
  const [filteredStats, setFilteredStats] = useState<MonthlyStats | null>(null)
  const { user } = useAuth()

  useEffect(() => {
    fetchStats()
  }, [])

  useEffect(() => {
    filterStats()
  }, [stats, selectedYear, selectedMonth])

  const fetchStats = async () => {
    try {
      setLoading(true)
      const installerRole = user?.role === 'installer1' ? 'installer1' : 'installer2'
      
      const { data: jobs, error } = await supabase
        .from('jobs')
        .select('*')
        .eq('assigned_installer', installerRole)
        .eq('job_status', 'ready')

      if (error) throw error

      // Group jobs by year and month
      const groupedStats: { [key: string]: MonthlyStats } = {}
      
      jobs?.forEach(job => {
        const jobDate = new Date(job.date)
        const year = jobDate.getFullYear()
        const month = jobDate.getMonth() + 1
        const key = `${year}-${month}`
        
        if (!groupedStats[key]) {
          groupedStats[key] = {
            year,
            month,
            totalJobs: 0,
            completedJobs: 0,
            notCompletedJobs: 0,
            pendingJobs: 0,
          }
        }
        
        groupedStats[key].totalJobs++
        
        if (job.completion_status === 'completed') {
          groupedStats[key].completedJobs++
        } else if (job.completion_status === 'not_completed') {
          groupedStats[key].notCompletedJobs++
        } else {
          groupedStats[key].pendingJobs++
        }
      })
      
      const statsArray = Object.values(groupedStats).sort((a, b) => {
        if (a.year !== b.year) return b.year - a.year
        return b.month - a.month
      })
      
      setStats(statsArray)
    } catch (error) {
      console.error('Error fetching stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const filterStats = () => {
    const filtered = stats.find(stat => 
      stat.year.toString() === selectedYear && 
      stat.month.toString() === selectedMonth
    )
    setFilteredStats(filtered || null)
  }

  const getAvailableYears = () => {
    const years = [...new Set(stats.map(stat => stat.year))]
    return years.sort((a, b) => b - a)
  }

  const getAvailableMonths = () => {
    const months = [...new Set(stats
      .filter(stat => stat.year.toString() === selectedYear)
      .map(stat => stat.month)
    )]
    return months.sort((a, b) => b - a)
  }

  const getMonthName = (month: number) => {
    const monthNames = [
      'کانوونی دووەم', 'شوبات', 'ئازار', 'نیسان', 'ئایار', 'حوزەیران',
      'تەمووز', 'ئاب', 'ئەیلوول', 'تشرینی یەکەم', 'تشرینی دووەم', 'کانوونی یەکەم'
    ]
    return monthNames[month - 1] || month.toString()
  }

  const StatCard = ({ 
    title, 
    value, 
    icon, 
    color = '#007AFF',
    percentage 
  }: {
    title: string
    value: number
    icon: string
    color?: string
    percentage?: number
  }) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <View style={styles.statHeader}>
        <Ionicons name={icon as any} size={24} color={color} />
        <Text style={styles.statValue}>{value}</Text>
      </View>
      <Text style={styles.statTitle}>{title}</Text>
      {percentage !== undefined && (
        <Text style={styles.statPercentage}>{percentage.toFixed(1)}%</Text>
      )}
    </View>
  )

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>ئاماری کارەکان</Text>
        <Text style={styles.headerSubtitle}>{user?.display_name}</Text>
      </View>

      <View style={styles.filtersContainer}>
        <View style={styles.filterItem}>
          <Text style={styles.filterLabel}>ساڵ:</Text>
          <Picker
            selectedValue={selectedYear}
            onValueChange={setSelectedYear}
            style={styles.picker}
          >
            {getAvailableYears().map(year => (
              <Picker.Item key={year} label={year.toString()} value={year.toString()} />
            ))}
          </Picker>
        </View>
        
        <View style={styles.filterItem}>
          <Text style={styles.filterLabel}>مانگ:</Text>
          <Picker
            selectedValue={selectedMonth}
            onValueChange={setSelectedMonth}
            style={styles.picker}
          >
            {getAvailableMonths().map(month => (
              <Picker.Item 
                key={month} 
                label={getMonthName(month)} 
                value={month.toString()} 
              />
            ))}
          </Picker>
        </View>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={fetchStats} />
        }
      >
        {filteredStats ? (
          <>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>
                {getMonthName(filteredStats.month)} {filteredStats.year}
              </Text>
              
              <View style={styles.statsGrid}>
                <StatCard
                  title="کۆی کارەکان"
                  value={filteredStats.totalJobs}
                  icon="briefcase-outline"
                  color="#007AFF"
                />
                <StatCard
                  title="تەواو بووە"
                  value={filteredStats.completedJobs}
                  icon="checkmark-circle-outline"
                  color="#28a745"
                  percentage={filteredStats.totalJobs > 0 ? (filteredStats.completedJobs / filteredStats.totalJobs) * 100 : 0}
                />
                <StatCard
                  title="تەواو نەبووە"
                  value={filteredStats.notCompletedJobs}
                  icon="close-circle-outline"
                  color="#dc3545"
                  percentage={filteredStats.totalJobs > 0 ? (filteredStats.notCompletedJobs / filteredStats.totalJobs) * 100 : 0}
                />
                <StatCard
                  title="چاوەڕوانە"
                  value={filteredStats.pendingJobs}
                  icon="time-outline"
                  color="#ffc107"
                  percentage={filteredStats.totalJobs > 0 ? (filteredStats.pendingJobs / filteredStats.totalJobs) * 100 : 0}
                />
              </View>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>کورتەی ئەدا</Text>
              <View style={styles.summaryCard}>
                <Text style={styles.summaryText}>
                  لە کۆی {filteredStats.totalJobs} کارەکە:
                </Text>
                <Text style={styles.summaryText}>
                  • {filteredStats.completedJobs} کارە تەواو کردووە 
                  ({filteredStats.totalJobs > 0 ? ((filteredStats.completedJobs / filteredStats.totalJobs) * 100).toFixed(1) : 0}%)
                </Text>
                <Text style={styles.summaryText}>
                  • {filteredStats.notCompletedJobs} کارە تەواو نەکردووە 
                  ({filteredStats.totalJobs > 0 ? ((filteredStats.notCompletedJobs / filteredStats.totalJobs) * 100).toFixed(1) : 0}%)
                </Text>
                <Text style={styles.summaryText}>
                  • {filteredStats.pendingJobs} کارە چاوەڕوانە 
                  ({filteredStats.totalJobs > 0 ? ((filteredStats.pendingJobs / filteredStats.totalJobs) * 100).toFixed(1) : 0}%)
                </Text>
              </View>
            </View>
          </>
        ) : (
          <View style={styles.noDataContainer}>
            <Ionicons name="bar-chart-outline" size={64} color="#ccc" />
            <Text style={styles.noDataText}>
              هیچ زانیارییەک بۆ {getMonthName(parseInt(selectedMonth))} {selectedYear} نەدۆزرایەوە
            </Text>
          </View>
        )}

        {/* All Time Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>کورتەی گشتی</Text>
          <View style={styles.allTimeGrid}>
            {stats.map((stat, index) => (
              <View key={`${stat.year}-${stat.month}`} style={styles.monthCard}>
                <Text style={styles.monthTitle}>
                  {getMonthName(stat.month)} {stat.year}
                </Text>
                <View style={styles.monthStats}>
                  <Text style={styles.monthStat}>کۆ: {stat.totalJobs}</Text>
                  <Text style={[styles.monthStat, { color: '#28a745' }]}>
                    تەواو: {stat.completedJobs}
                  </Text>
                  <Text style={[styles.monthStat, { color: '#dc3545' }]}>
                    تەواو نەبوو: {stat.notCompletedJobs}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: 'white',
    padding: 16,
    paddingTop: 50,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 4,
  },
  filtersContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: 'white',
  },
  filterItem: {
    flex: 1,
    marginHorizontal: 8,
  },
  filterLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
    textAlign: 'right',
  },
  picker: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    width: '48%',
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  statTitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'right',
  },
  statPercentage: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
    textAlign: 'right',
  },
  summaryCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  summaryText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 8,
    textAlign: 'right',
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100,
  },
  noDataText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
  allTimeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  monthCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    width: '48%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  monthTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 8,
  },
  monthStats: {
    alignItems: 'center',
  },
  monthStat: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
})

export default InstallerStatsScreen
