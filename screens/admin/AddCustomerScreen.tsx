import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  Platform,
  Modal,
} from 'react-native'
import { Picker } from '@react-native-picker/picker'
import DateTimePicker from '@react-native-community/datetimepicker'
import * as Location from 'expo-location'
import { LocationService } from '../../lib/locationService'
import * as ImagePicker from 'expo-image-picker'
import { Ionicons } from '@expo/vector-icons'
import LeafletMap from '../../components/LeafletMap'
import { supabase } from '../../lib/supabase'
import { useAuth } from '../../contexts/AuthContext'
import { ImageUploadService } from '../../lib/imageUploadService'

interface Place {
  id: string
  name: string
}

interface Area {
  id: string
  name: string
  place_id: string
}

interface ReferralSource {
  id: string
  name: string
}

const AddCustomerScreen = () => {
  const [date, setDate] = useState(new Date())
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [customerName, setCustomerName] = useState('')
  const [customerPhone, setCustomerPhone] = useState('')
  const [places, setPlaces] = useState<Place[]>([])
  const [areas, setAreas] = useState<Area[]>([])
  const [referralSources, setReferralSources] = useState<ReferralSource[]>([])
  const [selectedPlace, setSelectedPlace] = useState('')
  const [selectedArea, setSelectedArea] = useState('')
  const [selectedReferralSource, setSelectedReferralSource] = useState('')
  const [assignedInstaller, setAssignedInstaller] = useState('installer1')
  const [jobStatus, setJobStatus] = useState('not_ready')
  const [location, setLocation] = useState<{ latitude: number; longitude: number; accuracy?: number } | null>(null)
  const [images, setImages] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [uploadingImages, setUploadingImages] = useState(false)
  const [uploadProgress, setUploadProgress] = useState({ completed: 0, total: 0 })
  const [newPlaceName, setNewPlaceName] = useState('')
  const [newAreaName, setNewAreaName] = useState('')
  const [newReferralSourceName, setNewReferralSourceName] = useState('')
  const [showAddPlace, setShowAddPlace] = useState(false)
  const [showAddArea, setShowAddArea] = useState(false)
  const [showAddReferralSource, setShowAddReferralSource] = useState(false)
  const [showManageReferralSources, setShowManageReferralSources] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const { user } = useAuth()

  useEffect(() => {
    fetchPlaces()
    fetchReferralSources()
    requestLocationPermission()
  }, [])

  useEffect(() => {
    if (selectedPlace) {
      fetchAreas(selectedPlace)
    }
  }, [selectedPlace])

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync()
      if (status !== 'granted') {
        Alert.alert(
          'ڕێگەپێدان بە شوێن پێویستە',
          'بۆ دیاریکردنی شوێنەکە لە نەخشە، پێویستە ڕێگەپێدان بە شوێن بدرێت. دەتوانیت لە ڕێکخستنەکانی ئەپەکە ئەمە چالاک بکەیت.',
          [
            { text: 'باشە', style: 'default' }
          ]
        )
        return false
      }
      return true
    } catch (error) {
      console.error('Error requesting location permission:', error)
      return false
    }
  }

  const fetchPlaces = async () => {
    try {
      const { data, error } = await supabase
        .from('places')
        .select('*')
        .order('name')

      if (error) throw error
      setPlaces(data || [])
    } catch (error) {
      console.error('Error fetching places:', error)
    }
  }

  const fetchAreas = async (placeId: string) => {
    try {
      const { data, error } = await supabase
        .from('areas')
        .select('*')
        .eq('place_id', placeId)
        .order('name')

      if (error) throw error
      setAreas(data || [])
    } catch (error) {
      console.error('Error fetching areas:', error)
    }
  }

  const fetchReferralSources = async () => {
    try {
      const { data, error } = await supabase
        .from('referral_sources')
        .select('*')
        .order('name')

      if (error) {
        console.error('Error fetching referral sources:', error)
        // If table doesn't exist, set empty array
        setReferralSources([])
        return
      }
      setReferralSources(data || [])
    } catch (error) {
      console.error('Error fetching referral sources:', error)
      setReferralSources([])
    }
  }

  const addNewPlace = async () => {
    if (!newPlaceName.trim()) {
      Alert.alert('هەڵە', 'ناوی شوێن داخڵ بکە')
      return
    }

    try {
      const { data, error } = await supabase
        .from('places')
        .insert([{ name: newPlaceName.trim() }])
        .select()

      if (error) throw error

      setPlaces([...places, data[0]])
      setSelectedPlace(data[0].id)
      setNewPlaceName('')
      setShowAddPlace(false)
      Alert.alert('سەرکەوتوو', 'شوێنی نوێ زیادکرا')
    } catch (error) {
      console.error('Error adding place:', error)
      Alert.alert('هەڵە', 'نەتوانرا شوێنی نوێ زیاد بکرێت')
    }
  }

  const addNewArea = async () => {
    if (!newAreaName.trim() || !selectedPlace) {
      Alert.alert('هەڵە', 'ناوی ناوچە و شوێن دیاری بکە')
      return
    }

    try {
      const { data, error } = await supabase
        .from('areas')
        .insert([{ name: newAreaName.trim(), place_id: selectedPlace }])
        .select()

      if (error) throw error

      setAreas([...areas, data[0]])
      setSelectedArea(data[0].id)
      setNewAreaName('')
      setShowAddArea(false)
      Alert.alert('سەرکەوتوو', 'ناوچەی نوێ زیادکرا')
    } catch (error) {
      console.error('Error adding area:', error)
      Alert.alert('هەڵە', 'نەتوانرا ناوچەی نوێ زیاد بکرێت')
    }
  }

  const addNewReferralSource = async () => {
    if (!newReferralSourceName.trim()) {
      Alert.alert('هەڵە', 'ناوی سەرچاوەی ناردن داخڵ بکە')
      return
    }

    try {
      const { data, error } = await supabase
        .from('referral_sources')
        .insert([{ name: newReferralSourceName.trim() }])
        .select()

      if (error) {
        console.error('Error adding referral source:', error)
        if (error.message.includes('relation "referral_sources" does not exist')) {
          Alert.alert('هەڵە', 'خشتەی سەرچاوەی ناردن دروست نەکراوە. تکایە بەڕێوەبەر پەیوەندی بکە.')
        } else {
          Alert.alert('هەڵە', 'نەتوانرا سەرچاوەی ناردنی نوێ زیاد بکرێت')
        }
        return
      }

      if (data && data[0]) {
        setReferralSources([...referralSources, data[0]])
        setSelectedReferralSource(data[0].id)
        setNewReferralSourceName('')
        setShowAddReferralSource(false)
        Alert.alert('سەرکەوتوو', 'سەرچاوەی ناردنی نوێ زیادکرا')
      }
    } catch (error) {
      console.error('Error adding referral source:', error)
      Alert.alert('هەڵە', 'نەتوانرا سەرچاوەی ناردنی نوێ زیاد بکرێت')
    }
  }

  const deleteReferralSource = async (sourceId: string, sourceName: string) => {
    // First check if this referral source is being used by any jobs
    try {
      const { data: jobsUsingSource, error: checkError } = await supabase
        .from('jobs')
        .select('id')
        .eq('referral_source_id', sourceId)
        .limit(1)

      if (checkError) {
        console.error('Error checking referral source usage:', checkError)
        Alert.alert('هەڵە', 'نەتوانرا بەکارهێنانی سەرچاوەکە بپشکنرێت')
        return
      }

      if (jobsUsingSource && jobsUsingSource.length > 0) {
        Alert.alert(
          'ناتوانرێت بسڕدرێتەوە',
          `سەرچاوەی "${sourceName}" لە لایەن کارێک یان زیاتر بەکار دەهێنرێت. ناتوانرێت بسڕدرێتەوە.`,
          [{ text: 'باشە', style: 'default' }]
        )
        return
      }

      // Show confirmation dialog
      Alert.alert(
        'سڕینەوەی سەرچاوەی ناردن',
        `ئایا دڵنیایت لە سڕینەوەی "${sourceName}"؟\n\nئەم کردارە ناگەڕێتەوە.`,
        [
          { text: 'پاشگەزبوونەوە', style: 'cancel' },
          {
            text: 'سڕینەوە',
            style: 'destructive',
            onPress: () => performDeleteReferralSource(sourceId, sourceName)
          }
        ]
      )
    } catch (error) {
      console.error('Error in deleteReferralSource:', error)
      Alert.alert('هەڵە', 'هەڵەیەک ڕوویدا لە پشکنینی سەرچاوەکە')
    }
  }

  const performDeleteReferralSource = async (sourceId: string, sourceName: string) => {
    try {
      const { error } = await supabase
        .from('referral_sources')
        .delete()
        .eq('id', sourceId)

      if (error) {
        console.error('Error deleting referral source:', error)
        Alert.alert('هەڵە', 'نەتوانرا سەرچاوەکە بسڕدرێتەوە')
        return
      }

      // Update local state
      setReferralSources(referralSources.filter(source => source.id !== sourceId))

      // Clear selection if the deleted source was selected
      if (selectedReferralSource === sourceId) {
        setSelectedReferralSource('')
      }

      Alert.alert('سەرکەوتوو', `سەرچاوەی "${sourceName}" بە سەرکەوتوویی سڕایەوە`)
    } catch (error) {
      console.error('Error deleting referral source:', error)
      Alert.alert('هەڵە', 'نەتوانرا سەرچاوەکە بسڕدرێتەوە')
    }
  }

  const getCurrentLocation = async () => {
    try {
      const locationResult = await LocationService.getCurrentLocation({
        showProgress: true,
        fallbackToManual: true
      })

      if (locationResult) {
        setLocation({
          latitude: locationResult.latitude,
          longitude: locationResult.longitude,
          accuracy: locationResult.accuracy
        })

        const accuracyText = locationResult.accuracy
          ? ` (${LocationService.formatAccuracy(locationResult.accuracy)})`
          : ''

        Alert.alert(
          'سەرکەوتوو',
          `شوێنەکەت دیاری کرا بە ${locationResult.method}${accuracyText}\n\nدەتوانیت لە نەخشەکە شوێنەکە ڕاست بکەیتەوە ئەگەر پێویست بوو.`
        )
      } else {
        // Location failed, offer manual mode
        Alert.alert(
          'دیاریکردنی شوێن سەرکەوتوو نەبوو',
          'دەتوانیت شوێنەکە بە دەست لە نەخشەکە دیاری بکەیت.',
          [
            { text: 'دووبارە هەوڵ بدەرەوە', style: 'default', onPress: getCurrentLocation },
            {
              text: 'شوێن بە دەست دیاری بکە',
              style: 'default',
              onPress: () => setManualLocationMode(true)
            }
          ]
        )
      }
    } catch (error) {
      console.error('Error getting location:', error)
      Alert.alert(
        'هەڵە',
        'هەڵەیەک ڕوویدا لە دیاریکردنی شوێن. دەتوانیت شوێنەکە بە دەست دیاری بکەیت.',
        [
          { text: 'دووبارە هەوڵ بدەرەوە', style: 'default', onPress: getCurrentLocation },
          {
            text: 'شوێن بە دەست دیاری بکە',
            style: 'default',
            onPress: () => setManualLocationMode(true)
          }
        ]
      )
    }
  }

  const setManualLocationMode = (enable: boolean) => {
    if (enable) {
      const defaultLocation = LocationService.getDefaultLocation()
      setLocation({
        latitude: defaultLocation.latitude,
        longitude: defaultLocation.longitude,
        accuracy: defaultLocation.accuracy
      })
      Alert.alert(
        'دیاریکردنی شوێن بە دەست',
        'شوێنێکی بنەڕەتی لە نەخشەکە دانراوە. دەتوانیت لە نەخشەکە کلیک بکەیت یان نیشانەکە ڕابکێشیت بۆ دیاریکردنی شوێنی دروست.',
        [{ text: 'باشە', style: 'default' }]
      )
    }
  }

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsMultipleSelection: true,
      quality: 0.8,
    })

    if (!result.canceled) {
      const newImages = result.assets.map(asset => asset.uri)
      setImages([...images, ...newImages])
    }
  }

  const takePhoto = async () => {
    const result = await ImagePicker.launchCameraAsync({
      quality: 0.8,
    })

    if (!result.canceled) {
      setImages([...images, result.assets[0].uri])
    }
  }

  const clearValidationError = (field: string) => {
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const validateForm = () => {
    const errors: {[key: string]: string} = {}

    // Validate required fields
    if (!customerName.trim()) {
      errors.customerName = 'ناوی خاوەن ماڵ پێویستە'
    }

    if (!selectedReferralSource) {
      errors.referralSource = 'دیاریکردنی سەرچاوەی ناردن پێویستە'
    }

    if (!selectedPlace) {
      errors.place = 'دیاریکردنی شوێن پێویستە'
    }

    // Area is now optional - removed validation

    // Validate location for ready jobs
    if (jobStatus === 'ready' && !location) {
      errors.location = 'پێویستە شوێنەکە دیاری بکرێت بۆ کاری ئامادە'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const saveJob = async () => {
    if (!validateForm()) {
      const errorMessages = Object.values(validationErrors).join('\n')
      Alert.alert('هەڵە لە پڕکردنەوەی فۆرم', errorMessages)
      return
    }

    setLoading(true)
    let uploadedImageUrls: string[] = []

    try {
      // First, upload images if any exist
      if (images.length > 0) {
        setUploadingImages(true)
        setUploadProgress({ completed: 0, total: images.length })

        // WORKAROUND: Try to upload images, but don't fail if bucket creation fails
        try {
          // Check bucket access and create if needed
          const bucketExists = await ImageUploadService.checkBucketAccess()
          if (!bucketExists) {
            console.log('Bucket does not exist, attempting to create...')
            const bucketCreated = await ImageUploadService.createBucket()
            if (!bucketCreated) {
              console.log('Bucket creation failed, but continuing with job save...')
              // Don't throw error, just log and continue
            }
          }

          // Upload images with progress tracking
          const uploadResults = await ImageUploadService.uploadMultipleImages(
            images,
            undefined, // We'll update with job ID later if needed
            (completed, total) => {
              setUploadProgress({ completed, total })
            }
          )

          // Check for upload failures
          const failedUploads = uploadResults.filter(result => !result.success)
          if (failedUploads.length > 0) {
            console.error('Some images failed to upload:', failedUploads)

            // If ALL images failed, it's likely a storage configuration issue
            if (failedUploads.length === images.length) {
              Alert.alert(
                'کێشەی بارکردنی وێنەکان',
                'نەتوانرا وێنەکان بار بکرێن بەهۆی کێشەی پەیوەندی لەگەڵ خەزنکردن. دەتەوێت کارەکە بەبێ وێنەکان خەزن بکەیت؟',
                [
                  { text: 'نەخێر', style: 'cancel', onPress: () => setLoading(false) },
                  {
                    text: 'بەڵێ، بەبێ وێنەکان',
                    style: 'default',
                    onPress: () => {
                      uploadedImageUrls = [] // Save without images
                      proceedWithSave()
                    }
                  }
                ]
              )
              return
            } else {
              // Some images uploaded successfully
              Alert.alert(
                'هەڵە لە بارکردنی وێنەکان',
                `${failedUploads.length} وێنە نەتوانرا بار بکرێت. دەتەوێت بەردەوام بیت؟`,
                [
                  { text: 'نەخێر', style: 'cancel', onPress: () => setLoading(false) },
                  { text: 'بەڵێ', style: 'default', onPress: () => proceedWithSave() }
                ]
              )
              return
            }
          }

          // Extract successful URLs
          uploadedImageUrls = uploadResults
            .filter(result => result.success && result.url)
            .map(result => result.url!)

        } catch (imageError) {
          console.error('Image upload error:', imageError)

          // Show user-friendly error and option to continue without images
          Alert.alert(
            'کێشەی بارکردنی وێنەکان',
            'نەتوانرا وێنەکان بار بکرێن. دەتەوێت کارەکە بەبێ وێنەکان خەزن بکەیت؟',
            [
              { text: 'نەخێر', style: 'cancel', onPress: () => setLoading(false) },
              {
                text: 'بەڵێ، بەبێ وێنەکان',
                style: 'default',
                onPress: () => {
                  uploadedImageUrls = [] // Save without images
                  proceedWithSave()
                }
              }
            ]
          )
          return
        }

        setUploadingImages(false)
      }

      await proceedWithSave()

    } catch (error) {
      console.error('Error in saveJob:', error)
      Alert.alert('هەڵە', error instanceof Error ? error.message : 'نەتوانرا کارەکە خەزن بکرێت')
      setLoading(false)
      setUploadingImages(false)
    }

    async function proceedWithSave() {
      try {
        const jobData = {
          date: date.toISOString().split('T')[0],
          customer_name: customerName.trim(),
          customer_phone: customerPhone.trim(),
          place_id: selectedPlace,
          area_id: selectedArea || null,
          referral_source_id: selectedReferralSource || null,
          assigned_installer: assignedInstaller,
          job_status: jobStatus,
          latitude: location?.latitude,
          longitude: location?.longitude,
          images: uploadedImageUrls, // Use uploaded URLs instead of local URIs
          created_by: user?.id,
        }

        const { error } = await supabase
          .from('jobs')
          .insert([jobData])

        if (error) throw error

        Alert.alert('سەرکەوتوو', 'کارەکە خەزن کرا')

        // Reset form
        setCustomerName('')
        setCustomerPhone('')
        setSelectedPlace('')
        setSelectedArea('')
        setSelectedReferralSource('')
        setLocation(null)
        setImages([])
        setJobStatus('not_ready')
        setDate(new Date())
        setUploadProgress({ completed: 0, total: 0 })
      } catch (error) {
        console.error('Error saving job to database:', error)
        Alert.alert('هەڵە', 'نەتوانرا کارەکە خەزن بکرێت')
      } finally {
        setLoading(false)
        setUploadingImages(false)
      }
    }
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Ionicons name="person-add" size={28} color="#007AFF" />
          <Text style={styles.headerTitle}>زیادکردنی کڕیاری نوێ</Text>
        </View>
        <Text style={styles.headerSubtitle}>زانیاری کڕیار و شوێنەکە داخڵ بکە</Text>
      </View>

      <View style={styles.form}>
        {/* Date Picker */}
        <View style={styles.inputGroup}>
          <View style={styles.labelContainer}>
            <Ionicons name="calendar" size={18} color="#007AFF" />
            <Text style={styles.label}>بەروار:</Text>
          </View>
          <TouchableOpacity
            style={styles.dateButton}
            onPress={() => setShowDatePicker(true)}
          >
            <Text style={styles.dateText}>{date.toLocaleDateString('ku')}</Text>
            <Ionicons name="calendar-outline" size={20} color="#007AFF" />
          </TouchableOpacity>
        </View>

        {showDatePicker && (
          <DateTimePicker
            value={date}
            mode="date"
            display="default"
            onChange={(event, selectedDate) => {
              setShowDatePicker(Platform.OS === 'ios')
              if (selectedDate) setDate(selectedDate)
            }}
          />
        )}

        {/* Customer Name */}
        <View style={styles.inputGroup}>
          <View style={styles.labelContainer}>
            <Ionicons name="person" size={18} color="#28a745" />
            <Text style={styles.label}>ناوی خاوەن ماڵ:</Text>
            <Text style={styles.requiredIndicator}>*</Text>
          </View>
          <View style={[styles.inputContainer, validationErrors.customerName && styles.inputError]}>
            <TextInput
              style={styles.input}
              value={customerName}
              onChangeText={(text) => {
                setCustomerName(text)
                clearValidationError('customerName')
              }}
              placeholder="ناوی خاوەن ماڵ داخڵ بکە"
              textAlign="right"
            />
            <Ionicons name="person-outline" size={20} color="#ccc" style={styles.inputIcon} />
          </View>
          {validationErrors.customerName && (
            <Text style={styles.errorText}>{validationErrors.customerName}</Text>
          )}
        </View>

        {/* Customer Phone */}
        <View style={styles.inputGroup}>
          <View style={styles.labelContainer}>
            <Ionicons name="call" size={18} color="#FF6B35" />
            <Text style={styles.label}>ژمارەی موبایل:</Text>
          </View>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              value={customerPhone}
              onChangeText={setCustomerPhone}
              placeholder="ژمارەی موبایل داخڵ بکە"
              keyboardType="phone-pad"
              textAlign="right"
            />
            <Ionicons name="call-outline" size={20} color="#ccc" style={styles.inputIcon} />
          </View>
        </View>

        {/* Referral Source Selection */}
        <View style={styles.inputGroup}>
          <View style={styles.labelContainer}>
            <Ionicons name="people" size={18} color="#17a2b8" />
            <Text style={styles.label}>لە لایەن:</Text>
            <Text style={styles.requiredIndicator}>*</Text>
          </View>
          <View style={styles.pickerWrapper}>
            <View style={[styles.pickerContainer, validationErrors.referralSource && styles.inputError]}>
              <Picker
                selectedValue={selectedReferralSource}
                onValueChange={(value) => {
                  setSelectedReferralSource(value)
                  clearValidationError('referralSource')
                }}
                style={styles.picker}
                itemStyle={styles.pickerItem}
                mode="dropdown"
                dropdownIconColor="#666"
              >
                <Picker.Item
                  label="سەرچاوەی ناردن دیاری بکە"
                  value=""
                  style={styles.pickerItemStyle}
                />
                {referralSources.map(source => (
                  <Picker.Item
                    key={source.id}
                    label={source.name}
                    value={source.id}
                    style={styles.pickerItemStyle}
                  />
                ))}
              </Picker>
            </View>
          </View>
          {validationErrors.referralSource && (
            <Text style={styles.errorText}>{validationErrors.referralSource}</Text>
          )}
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.addButton, styles.addButtonHalf]}
              onPress={() => setShowAddReferralSource(true)}
            >
              <Ionicons name="add-circle" size={16} color="white" />
              <Text style={styles.addButtonText}>زیاد بکە</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.addButton, styles.manageButton, styles.addButtonHalf]}
              onPress={() => setShowManageReferralSources(true)}
            >
              <Ionicons name="settings" size={16} color="white" />
              <Text style={styles.addButtonText}>بەڕێوەبردن</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Place Selection */}
        <View style={styles.inputGroup}>
          <View style={styles.labelContainer}>
            <Ionicons name="business" size={18} color="#6f42c1" />
            <Text style={styles.label}>شوێن:</Text>
            <Text style={styles.requiredIndicator}>*</Text>
          </View>
          <View style={styles.pickerWrapper}>
            <View style={[styles.pickerContainer, validationErrors.place && styles.inputError]}>
              <Picker
                selectedValue={selectedPlace}
                onValueChange={(value) => {
                  setSelectedPlace(value)
                  clearValidationError('place')
                  // Clear area selection when place changes
                  if (selectedArea) {
                    setSelectedArea('')
                    clearValidationError('area')
                  }
                }}
                style={styles.picker}
                itemStyle={styles.pickerItem}
                mode="dropdown"
                dropdownIconColor="#666"
              >
                <Picker.Item
                  label="شوێن دیاری بکە"
                  value=""
                  style={styles.pickerItemStyle}
                />
                {places.map(place => (
                  <Picker.Item
                    key={place.id}
                    label={place.name}
                    value={place.id}
                    style={styles.pickerItemStyle}
                  />
                ))}
              </Picker>
            </View>
          </View>
          {validationErrors.place && (
            <Text style={styles.errorText}>{validationErrors.place}</Text>
          )}
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowAddPlace(true)}
          >
            <Ionicons name="add-circle" size={16} color="white" />
            <Text style={styles.addButtonText}>شوێنی نوێ زیاد بکە</Text>
          </TouchableOpacity>
        </View>

        {/* Area Selection */}
        <View style={styles.inputGroup}>
          <View style={styles.labelContainer}>
            <Ionicons name="location" size={18} color="#dc3545" />
            <Text style={styles.label}>ناوچە:</Text>
          </View>
          <View style={styles.pickerWrapper}>
            <View style={[styles.pickerContainer, validationErrors.area && styles.inputError]}>
              <Picker
                selectedValue={selectedArea}
                onValueChange={(value) => {
                  setSelectedArea(value)
                  clearValidationError('area')
                }}
                style={styles.picker}
                itemStyle={styles.pickerItem}
                mode="dropdown"
                dropdownIconColor="#666"
                enabled={!!selectedPlace}
              >
                <Picker.Item
                  label="ناوچە دیاری بکە"
                  value=""
                  style={styles.pickerItemStyle}
                />
                {areas.map(area => (
                  <Picker.Item
                    key={area.id}
                    label={area.name}
                    value={area.id}
                    style={styles.pickerItemStyle}
                  />
                ))}
              </Picker>
            </View>
          </View>
          {validationErrors.area && (
            <Text style={styles.errorText}>{validationErrors.area}</Text>
          )}
          <TouchableOpacity
            style={[styles.addButton, !selectedPlace && styles.addButtonDisabled]}
            onPress={() => setShowAddArea(true)}
            disabled={!selectedPlace}
          >
            <Ionicons name="add-circle" size={16} color="white" />
            <Text style={styles.addButtonText}>ناوچەی نوێ زیاد بکە</Text>
          </TouchableOpacity>
        </View>

        {/* Installer Assignment */}
        <View style={styles.inputGroup}>
          <View style={styles.labelContainer}>
            <Ionicons name="construct" size={18} color="#fd7e14" />
            <Text style={styles.label}>دیاری کردنی وەستا:</Text>
          </View>
          <View style={styles.pickerWrapper}>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={assignedInstaller}
                onValueChange={setAssignedInstaller}
                style={styles.picker}
                itemStyle={styles.pickerItem}
                mode="dropdown"
                dropdownIconColor="#666"
              >
                <Picker.Item
                  label="وەستای بەستن یەکەم"
                  value="installer1"
                  style={styles.pickerItemStyle}
                />
                <Picker.Item
                  label="وەستای بەستن دووەم"
                  value="installer2"
                  style={styles.pickerItemStyle}
                />
              </Picker>
            </View>
          </View>
        </View>

        {/* Location */}
        <View style={styles.inputGroup}>
          <View style={styles.labelContainer}>
            <Ionicons name="map" size={18} color="#20c997" />
            <Text style={styles.label}>نەخشە:</Text>
          </View>
          <TouchableOpacity
            style={styles.locationButton}
            onPress={getCurrentLocation}
          >
            <Ionicons name="location" size={20} color="white" />
            <Text style={styles.locationButtonText}>شوێنەکەم دیاری بکە</Text>
          </TouchableOpacity>

          <View style={styles.hintContainer}>
            <Ionicons name="information-circle" size={16} color="#6c757d" />
            <Text style={styles.locationHint}>
              کلیک بکە بۆ دیاریکردنی شوێنەکەت بە GPS، یان ئەگەر GPS کار نەکرد دەتوانیت لە نەخشەکە شوێنەکە بە دەست دیاری بکەیت.
            </Text>
          </View>

          {location && (
            <View style={styles.mapContainer}>
              <LeafletMap
                latitude={location.latitude}
                longitude={location.longitude}
                height={250}
                interactive={true}
                showAccuracy={true}
                accuracyRadius={location.accuracy || 0}
                showCurrentLocationButton={true}
                onLocationChange={(lat, lng) => {
                  setLocation({ latitude: lat, longitude: lng, accuracy: location.accuracy })
                }}
              />
              <Text style={styles.mapHint}>
                دەتوانیت لە نەخشەکە کلیک بکەیت یان نیشانەکە ڕابکێشیت بۆ گۆڕینی شوێنەکە
              </Text>
              {location.accuracy && location.accuracy > 0 && (
                <Text style={styles.accuracyText}>
                  وردی شوێن: {LocationService.formatAccuracy(location.accuracy)}
                </Text>
              )}
            </View>
          )}
        </View>

        {/* Job Status */}
        <View style={styles.inputGroup}>
          <View style={styles.labelContainer}>
            <Ionicons name="checkmark-circle" size={18} color="#17a2b8" />
            <Text style={styles.label}>دیاری بکە:</Text>
          </View>
          <View style={styles.pickerWrapper}>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={jobStatus}
                onValueChange={setJobStatus}
                style={styles.picker}
                itemStyle={styles.pickerItem}
                mode="dropdown"
                dropdownIconColor="#666"
              >
                <Picker.Item
                  label="حازر نییە بۆ بەستن"
                  value="not_ready"
                  style={styles.pickerItemStyle}
                />
                <Picker.Item
                  label="حازرە بۆ بەستن"
                  value="ready"
                  style={styles.pickerItemStyle}
                />
              </Picker>
            </View>
          </View>
        </View>

        {/* Image Upload */}
        <View style={styles.inputGroup}>
          <View style={styles.labelContainer}>
            <Ionicons name="images" size={18} color="#e83e8c" />
            <Text style={styles.label}>وێنەکان:</Text>
          </View>
          <View style={styles.imageButtons}>
            <TouchableOpacity style={[styles.imageButton, styles.cameraButton]} onPress={takePhoto}>
              <Ionicons name="camera" size={20} color="white" />
              <Text style={styles.imageButtonText}>وێنە بگرە</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.imageButton, styles.galleryButton]} onPress={pickImage}>
              <Ionicons name="images" size={20} color="white" />
              <Text style={styles.imageButtonText}>وێنە هەڵبژێرە</Text>
            </TouchableOpacity>
          </View>
          {images.length > 0 && (
            <View style={styles.imageCountContainer}>
              <Ionicons name="checkmark-circle" size={16} color="#28a745" />
              <Text style={styles.imageCount}>{images.length} وێنە هەڵبژێردراوە</Text>
            </View>
          )}
          {uploadingImages && (
            <View style={styles.uploadProgressContainer}>
              <Ionicons name="cloud-upload" size={16} color="#007AFF" />
              <Text style={styles.uploadProgressText}>
                بارکردنی وێنەکان... ({uploadProgress.completed}/{uploadProgress.total})
              </Text>
            </View>
          )}
        </View>

        {/* Save Button */}
        <TouchableOpacity
          style={[
            styles.saveButton,
            (loading || uploadingImages || (!customerName.trim() || !selectedReferralSource || !selectedPlace)) && styles.saveButtonDisabled
          ]}
          onPress={saveJob}
          disabled={loading || uploadingImages || (!customerName.trim() || !selectedReferralSource || !selectedPlace)}
        >
          <Ionicons
            name={uploadingImages ? "cloud-upload" : loading ? "hourglass" : "save"}
            size={20}
            color="white"
          />
          <Text style={styles.saveButtonText}>
            {uploadingImages ? 'بارکردنی وێنەکان...' : loading ? 'خەزن دەکرێت...' : 'خەزن کردن'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Add Place Modal */}
      <Modal
        visible={showAddPlace}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAddPlace(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>شوێنی نوێ زیاد بکە</Text>

            <TextInput
              style={styles.modalInput}
              placeholder="ناوی شوێن"
              value={newPlaceName}
              onChangeText={setNewPlaceName}
              textAlign="right"
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowAddPlace(false)
                  setNewPlaceName('')
                }}
              >
                <Text style={styles.cancelButtonText}>پاشگەزبوونەوە</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={addNewPlace}
              >
                <Text style={styles.confirmButtonText}>زیادکردن</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Add Area Modal */}
      <Modal
        visible={showAddArea}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAddArea(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>ناوچەی نوێ زیاد بکە</Text>

            <TextInput
              style={styles.modalInput}
              placeholder="ناوی ناوچە"
              value={newAreaName}
              onChangeText={setNewAreaName}
              textAlign="right"
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowAddArea(false)
                  setNewAreaName('')
                }}
              >
                <Text style={styles.cancelButtonText}>پاشگەزبوونەوە</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={addNewArea}
              >
                <Text style={styles.confirmButtonText}>زیادکردن</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Add Referral Source Modal */}
      <Modal
        visible={showAddReferralSource}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAddReferralSource(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>سەرچاوەی ناردنی نوێ زیاد بکە</Text>

            <TextInput
              style={styles.modalInput}
              placeholder="ناوی سەرچاوەی ناردن"
              value={newReferralSourceName}
              onChangeText={setNewReferralSourceName}
              textAlign="right"
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowAddReferralSource(false)
                  setNewReferralSourceName('')
                }}
              >
                <Text style={styles.cancelButtonText}>پاشگەزبوونەوە</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={addNewReferralSource}
              >
                <Text style={styles.confirmButtonText}>زیادکردن</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Manage Referral Sources Modal */}
      <Modal
        visible={showManageReferralSources}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowManageReferralSources(false)}
      >
        <View style={styles.modalContainer}>
          <View style={[styles.modalContent, styles.manageModalContent]}>
            <Text style={styles.modalTitle}>بەڕێوەبردنی سەرچاوەکانی ناردن</Text>

            <ScrollView style={styles.referralSourcesList}>
              {referralSources.map((source) => (
                <View key={source.id} style={styles.referralSourceItem}>
                  <Text style={styles.referralSourceName}>{source.name}</Text>
                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={() => deleteReferralSource(source.id, source.name)}
                  >
                    <Ionicons name="trash" size={18} color="white" />
                  </TouchableOpacity>
                </View>
              ))}

              {referralSources.length === 0 && (
                <Text style={styles.emptyText}>هیچ سەرچاوەیەک نییە</Text>
              )}
            </ScrollView>

            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton, styles.closeButton]}
              onPress={() => setShowManageReferralSources(false)}
            >
              <Text style={styles.cancelButtonText}>داخستن</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </ScrollView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: 'white',
    padding: 20,
    paddingTop: 50,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 12,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6c757d',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  form: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 24,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
    flex: 1,
    textAlign: 'right',
  },
  requiredIndicator: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#dc3545',
    marginLeft: 4,
  },
  inputContainer: {
    position: 'relative',
  },
  input: {
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 14,
    paddingRight: 45,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e9ecef',
    textAlign: 'right',
  },
  inputIcon: {
    position: 'absolute',
    left: 15,
    top: 17,
  },
  inputError: {
    borderColor: '#dc3545',
    borderWidth: 2,
  },
  errorText: {
    fontSize: 14,
    color: '#dc3545',
    marginTop: 8,
    textAlign: 'right',
    fontWeight: '500',
  },
  dateButton: {
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 14,
    borderWidth: 1,
    borderColor: '#e9ecef',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  pickerWrapper: {
    position: 'relative',
  },
  pickerContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#e9ecef',
    overflow: 'hidden',
    minHeight: 60,
    paddingHorizontal: 10,
  },
  picker: {
    height: 60,
    color: '#333',
    fontSize: 15,
    backgroundColor: 'transparent',
    marginLeft: 0,
    marginRight: 0,
    paddingHorizontal: 10,
    textAlign: 'right',
  },
  pickerItem: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
    textAlign: 'right',
    height: 60,
    paddingHorizontal: 15,
    paddingVertical: 15,
  },
  pickerItemStyle: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
    textAlign: 'right',
    paddingHorizontal: 15,
    paddingVertical: 15,
    height: 60,
    lineHeight: 20,
  },
  addButton: {
    backgroundColor: '#28a745',
    borderRadius: 8,
    padding: 12,
    marginTop: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  addButtonDisabled: {
    backgroundColor: '#6c757d',
  },
  addButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
    gap: 8,
  },
  addButtonHalf: {
    flex: 1,
    marginTop: 0,
  },
  manageButton: {
    backgroundColor: '#6f42c1',
  },
  locationButton: {
    backgroundColor: '#007AFF',
    borderRadius: 10,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 5,
    elevation: 4,
  },
  locationButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  hintContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 12,
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#17a2b8',
  },
  locationHint: {
    fontSize: 12,
    color: '#6c757d',
    marginLeft: 8,
    flex: 1,
    textAlign: 'right',
    lineHeight: 18,
  },
  mapContainer: {
    marginTop: 12,
    borderRadius: 8,
    overflow: 'hidden',
  },
  mapHint: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  accuracyText: {
    fontSize: 12,
    color: '#007AFF',
    marginTop: 4,
    textAlign: 'center',
    fontWeight: '500',
  },
  imageButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  imageButton: {
    borderRadius: 10,
    padding: 14,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  cameraButton: {
    backgroundColor: '#fd7e14',
  },
  galleryButton: {
    backgroundColor: '#6f42c1',
  },
  imageButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  imageCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    backgroundColor: '#d4edda',
    padding: 8,
    borderRadius: 8,
  },
  imageCount: {
    fontSize: 14,
    color: '#155724',
    fontWeight: '500',
    marginLeft: 6,
  },
  uploadProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    backgroundColor: '#e3f2fd',
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  uploadProgressText: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
    marginLeft: 6,
  },
  saveButton: {
    backgroundColor: '#28a745',
    borderRadius: 12,
    padding: 18,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 6,
  },
  saveButtonDisabled: {
    backgroundColor: '#6c757d',
  },
  saveButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    width: '90%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
  },
  modalInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'right',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 0.48,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#6c757d',
  },
  confirmButton: {
    backgroundColor: '#007AFF',
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  manageModalContent: {
    maxHeight: '80%',
  },
  referralSourcesList: {
    maxHeight: 300,
    marginBottom: 20,
  },
  referralSourceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  referralSourceName: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    flex: 1,
    textAlign: 'right',
  },
  deleteButton: {
    backgroundColor: '#dc3545',
    borderRadius: 6,
    padding: 8,
    marginLeft: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  emptyText: {
    textAlign: 'center',
    color: '#6c757d',
    fontSize: 16,
    fontStyle: 'italic',
    marginTop: 20,
  },
  closeButton: {
    width: '100%',
  },
})

export default AddCustomerScreen
