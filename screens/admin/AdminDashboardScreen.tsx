import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { supabase } from '../../lib/supabase'

interface DashboardStats {
  totalJobs: number
  readyJobs: number
  notReadyJobs: number
  completedJobs: number
  pendingJobs: number
  installer1Jobs: number
  installer2Jobs: number
  dispatchJobs: number
  todayJobs: number
  thisWeekJobs: number
  thisMonthJobs: number
}

const AdminDashboardScreen = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalJobs: 0,
    readyJobs: 0,
    notReadyJobs: 0,
    completedJobs: 0,
    pendingJobs: 0,
    installer1Jobs: 0,
    installer2Jobs: 0,
    dispatchJobs: 0,
    todayJobs: 0,
    thisWeekJobs: 0,
    thisMonthJobs: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      setLoading(true)

      // Get all jobs
      const { data: allJobs, error } = await supabase
        .from('jobs')
        .select('*')

      if (error) throw error

      const jobs = allJobs || []
      const today = new Date()
      const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()))
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)

      const newStats: DashboardStats = {
        totalJobs: jobs.length,
        readyJobs: jobs.filter(job => job.job_status === 'ready').length,
        notReadyJobs: jobs.filter(job => job.job_status === 'not_ready').length,
        completedJobs: jobs.filter(job => job.completion_status === 'completed').length,
        pendingJobs: jobs.filter(job => job.completion_status === 'pending').length,
        installer1Jobs: jobs.filter(job => job.assigned_installer === 'installer1' && job.job_status === 'ready').length,
        installer2Jobs: jobs.filter(job => job.assigned_installer === 'installer2' && job.job_status === 'ready').length,
        dispatchJobs: jobs.filter(job => job.assigned_to_dispatch === true).length,
        todayJobs: jobs.filter(job => {
          const jobDate = new Date(job.date)
          const today = new Date()
          return jobDate.toDateString() === today.toDateString()
        }).length,
        thisWeekJobs: jobs.filter(job => {
          const jobDate = new Date(job.date)
          return jobDate >= startOfWeek
        }).length,
        thisMonthJobs: jobs.filter(job => {
          const jobDate = new Date(job.date)
          return jobDate >= startOfMonth
        }).length,
      }

      setStats(newStats)
    } catch (error) {
      console.error('Error fetching stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const StatCard = ({ 
    title, 
    value, 
    icon, 
    color = '#007AFF',
    subtitle 
  }: {
    title: string
    value: number
    icon: string
    color?: string
    subtitle?: string
  }) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <View style={styles.statHeader}>
        <Ionicons name={icon as any} size={24} color={color} />
        <Text style={styles.statValue}>{value}</Text>
      </View>
      <Text style={styles.statTitle}>{title}</Text>
      {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
    </View>
  )

  const ProgressBar = ({ 
    label, 
    value, 
    total, 
    color = '#007AFF' 
  }: {
    label: string
    value: number
    total: number
    color?: string
  }) => {
    const percentage = total > 0 ? (value / total) * 100 : 0
    
    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressLabel}>{label}</Text>
          <Text style={styles.progressValue}>{value}/{total}</Text>
        </View>
        <View style={styles.progressBarContainer}>
          <View 
            style={[
              styles.progressBar, 
              { width: `${percentage}%`, backgroundColor: color }
            ]} 
          />
        </View>
        <Text style={styles.progressPercentage}>{percentage.toFixed(1)}%</Text>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>داشبۆرد</Text>
        <Text style={styles.headerSubtitle}>گشتی کارەکان</Text>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={fetchStats} />
        }
      >
        {/* Overview Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>گشتی</Text>
          <View style={styles.statsGrid}>
            <StatCard
              title="کۆی کارەکان"
              value={stats.totalJobs}
              icon="briefcase-outline"
              color="#007AFF"
            />
            <StatCard
              title="ئەمڕۆ"
              value={stats.todayJobs}
              icon="today-outline"
              color="#28a745"
            />
            <StatCard
              title="ئەم هەفتەیە"
              value={stats.thisWeekJobs}
              icon="calendar-outline"
              color="#ffc107"
            />
            <StatCard
              title="ئەم مانگە"
              value={stats.thisMonthJobs}
              icon="calendar-outline"
              color="#17a2b8"
            />
          </View>
        </View>

        {/* Job Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>دۆخی کارەکان</Text>
          <View style={styles.statsGrid}>
            <StatCard
              title="حازرە بۆ بەستن"
              value={stats.readyJobs}
              icon="checkmark-circle-outline"
              color="#28a745"
            />
            <StatCard
              title="حازر نییە"
              value={stats.notReadyJobs}
              icon="time-outline"
              color="#ffc107"
            />
            <StatCard
              title="تەواو بووە"
              value={stats.completedJobs}
              icon="checkmark-done-outline"
              color="#007AFF"
            />
            <StatCard
              title="چاوەڕوانە"
              value={stats.pendingJobs}
              icon="hourglass-outline"
              color="#6c757d"
            />
          </View>
        </View>

        {/* Installer Distribution */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>دابەشکردن بەسەر وەستاکان</Text>
          <View style={styles.statsGrid}>
            <StatCard
              title="وەستای یەکەم"
              value={stats.installer1Jobs}
              icon="person-outline"
              color="#007AFF"
            />
            <StatCard
              title="وەستای دووەم"
              value={stats.installer2Jobs}
              icon="person-outline"
              color="#28a745"
            />
            <StatCard
              title="ناردن"
              value={stats.dispatchJobs}
              icon="send-outline"
              color="#ffc107"
            />
          </View>
        </View>

        {/* Progress Bars */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>پێشکەوتن</Text>
          <View style={styles.progressSection}>
            <ProgressBar
              label="کارە تەواوبووەکان"
              value={stats.completedJobs}
              total={stats.totalJobs}
              color="#28a745"
            />
            <ProgressBar
              label="کارە ئامادەکان"
              value={stats.readyJobs}
              total={stats.totalJobs}
              color="#007AFF"
            />
            <ProgressBar
              label="وەستای یەکەم"
              value={stats.installer1Jobs}
              total={stats.readyJobs}
              color="#007AFF"
            />
            <ProgressBar
              label="وەستای دووەم"
              value={stats.installer2Jobs}
              total={stats.readyJobs}
              color="#28a745"
            />
          </View>
        </View>

        {/* Quick Stats Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>کورتەی خێرا</Text>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryText}>
              لە کۆی {stats.totalJobs} کارەکە، {stats.completedJobs} کارە تەواو بووە 
              ({stats.totalJobs > 0 ? ((stats.completedJobs / stats.totalJobs) * 100).toFixed(1) : 0}%)
            </Text>
            <Text style={styles.summaryText}>
              {stats.readyJobs} کارە ئامادەیە بۆ بەستن
            </Text>
            <Text style={styles.summaryText}>
              {stats.pendingJobs} کارە چاوەڕوانی تەواوکردنە
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  )
}

const { width } = Dimensions.get('window')
const cardWidth = (width - 48) / 2

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: 'white',
    padding: 16,
    paddingTop: 50,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 4,
  },
  content: {
    flex: 1,
  },
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'right',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    width: cardWidth,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  statTitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'right',
  },
  statSubtitle: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
    textAlign: 'right',
  },
  progressSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
  },
  progressValue: {
    fontSize: 14,
    color: '#666',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#e9ecef',
    borderRadius: 4,
    marginBottom: 4,
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  progressPercentage: {
    fontSize: 12,
    color: '#666',
    textAlign: 'right',
  },
  summaryCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  summaryText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
    marginBottom: 8,
    textAlign: 'right',
  },
})

export default AdminDashboardScreen
