import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  Modal,
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'
import { supabase } from '../../lib/supabase'
import { useAuth } from '../../contexts/AuthContext'

interface User {
  id: string
  username: string
  role: string
  display_name: string
}

const UserManagementScreen = () => {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  const [adminPassword, setAdminPassword] = useState('')
  const [authenticated, setAuthenticated] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [newUsername, setNewUsername] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const { user } = useAuth()

  useEffect(() => {
    if (authenticated) {
      fetchUsers()
    }
  }, [authenticated])

  const verifyAdminPassword = () => {
    if (adminPassword === '223344') {
      setAuthenticated(true)
      setShowPasswordModal(false)
      setAdminPassword('')
    } else {
      Alert.alert('هەڵە', 'وشەی نهێنی هەڵەیە')
    }
  }

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('app_users')
        .select('*')
        .order('role')

      if (error) throw error
      setUsers(data || [])
    } catch (error) {
      console.error('Error fetching users:', error)
      Alert.alert('هەڵە', 'نەتوانرا بەکارهێنەران بهێنرێنەوە')
    } finally {
      setLoading(false)
    }
  }

  const updateUser = async () => {
    if (!editingUser || !newUsername.trim()) {
      Alert.alert('هەڵە', 'ناوی بەکارهێنەر داخڵ بکە')
      return
    }

    try {
      const updateData: any = {
        username: newUsername.trim(),
        updated_at: new Date().toISOString()
      }

      // Only update password if provided
      if (newPassword.trim()) {
        updateData.password_hash = '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'
      }

      const { error } = await supabase
        .from('app_users')
        .update(updateData)
        .eq('id', editingUser.id)

      if (error) throw error

      Alert.alert('سەرکەوتوو', 'بەکارهێنەر نوێ کرایەوە')
      setEditingUser(null)
      setNewUsername('')
      setNewPassword('')
      fetchUsers()
    } catch (error) {
      console.error('Error updating user:', error)
      Alert.alert('هەڵە', 'نەتوانرا بەکارهێنەر نوێ بکرێتەوە')
    }
  }

  const startEditUser = (user: User) => {
    setEditingUser(user)
    setNewUsername(user.username)
    setNewPassword('')
  }

  const getRoleDisplayName = (role: string) => {
    const roleNames = {
      admin: 'کارگە',
      dispatch: 'ناردن',
      installer1: 'وەستای بەستن یەکەم',
      installer2: 'وەستای بەستن دووەم'
    }
    return roleNames[role as keyof typeof roleNames] || role
  }

  const getDefaultPassword = (username: string) => {
    const passwords = {
      ali: '223344',
      car: '123455',
      wasta1: '654321',
      wasta2: '123456788'
    }
    return passwords[username as keyof typeof passwords] || 'نەزانراو'
  }

  if (!authenticated) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>بەڕێوەبردنی بەکارهێنەران</Text>
        </View>

        <View style={styles.authContainer}>
          <Ionicons name="lock-closed" size={80} color="#007AFF" />
          <Text style={styles.authTitle}>پشتڕاستکردنەوەی ناسنامە</Text>
          <Text style={styles.authSubtitle}>
            بۆ دەستگەیشتن بە بەڕێوەبردنی بەکارهێنەران، وشەی نهێنی خۆت داخڵ بکە
          </Text>
          
          <TouchableOpacity
            style={styles.authButton}
            onPress={() => setShowPasswordModal(true)}
          >
            <Text style={styles.authButtonText}>چوونە ژوورەوە</Text>
          </TouchableOpacity>
        </View>

        <Modal
          visible={showPasswordModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowPasswordModal(false)}
        >
          <View style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>وشەی نهێنی داخڵ بکە</Text>
              
              <TextInput
                style={styles.passwordInput}
                placeholder="وشەی نهێنی"
                value={adminPassword}
                onChangeText={setAdminPassword}
                secureTextEntry
                textAlign="right"
              />
              
              <View style={styles.modalButtons}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.cancelButton]}
                  onPress={() => {
                    setShowPasswordModal(false)
                    setAdminPassword('')
                  }}
                >
                  <Text style={styles.cancelButtonText}>پاشگەزبوونەوە</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[styles.modalButton, styles.confirmButton]}
                  onPress={verifyAdminPassword}
                >
                  <Text style={styles.confirmButtonText}>پشتڕاستکردنەوە</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    )
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => setAuthenticated(false)}
          style={styles.backButton}
        >
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>بەڕێوەبردنی بەکارهێنەران</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        {users.map((user) => (
          <View key={user.id} style={styles.userCard}>
            <View style={styles.userInfo}>
              <Text style={styles.displayName}>{user.display_name}</Text>
              <Text style={styles.username}>ناوی بەکارهێنەر: {user.username}</Text>
              <Text style={styles.role}>ڕۆڵ: {getRoleDisplayName(user.role)}</Text>
              <Text style={styles.password}>
                وشەی نهێنی: {getDefaultPassword(user.username)}
              </Text>
            </View>
            
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => startEditUser(user)}
            >
              <Ionicons name="create-outline" size={20} color="white" />
              <Text style={styles.editButtonText}>دەستکاری</Text>
            </TouchableOpacity>
          </View>
        ))}
      </ScrollView>

      {/* Edit User Modal */}
      <Modal
        visible={!!editingUser}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setEditingUser(null)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>دەستکاری بەکارهێنەر</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>ناوی بەکارهێنەر:</Text>
              <TextInput
                style={styles.input}
                value={newUsername}
                onChangeText={setNewUsername}
                placeholder="ناوی بەکارهێنەر"
                textAlign="right"
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>وشەی نهێنی نوێ (ئیختیاری):</Text>
              <TextInput
                style={styles.input}
                value={newPassword}
                onChangeText={setNewPassword}
                placeholder="وشەی نهێنی نوێ"
                secureTextEntry
                textAlign="right"
              />
            </View>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setEditingUser(null)
                  setNewUsername('')
                  setNewPassword('')
                }}
              >
                <Text style={styles.cancelButtonText}>پاشگەزبوونەوە</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={updateUser}
              >
                <Text style={styles.confirmButtonText}>خەزن کردن</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: 'white',
    padding: 16,
    paddingTop: 50,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    padding: 8,
  },
  placeholder: {
    width: 40,
  },
  authContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  authTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 24,
    marginBottom: 16,
    textAlign: 'center',
  },
  authSubtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  authButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  authButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  userCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  userInfo: {
    flex: 1,
  },
  displayName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  username: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  role: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  password: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '600',
  },
  editButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  editButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    width: '90%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
  },
  passwordInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'right',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'right',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlign: 'right',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  modalButton: {
    flex: 0.48,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#6c757d',
  },
  confirmButton: {
    backgroundColor: '#007AFF',
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
})

export default UserManagementScreen
