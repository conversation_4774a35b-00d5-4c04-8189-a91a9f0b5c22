# Dependency Issues and Fixes

## Issues Found by expo-doctor

### 1. react-native-crypto (Unmaintained & Untested on New Architecture)

**Problem**: This package is no longer maintained and doesn't support React Native's New Architecture.

**Current Usage**: Likely used for cryptographic operations.

**Recommended Alternatives**:

#### Option A: Use Expo Crypto (Recommended)
```bash
npx expo install expo-crypto
```

#### Option B: Use react-native-quick-crypto (Modern alternative)
```bash
npm install react-native-quick-crypto
```

**Migration Steps**:
1. Install the alternative package
2. Update imports in your code:
   ```javascript
   // Old
   import crypto from 'react-native-crypto';
   
   // New (Expo Crypto)
   import * as Crypto from 'expo-crypto';
   
   // Or (react-native-quick-crypto)
   import { randomBytes, createHash } from 'react-native-quick-crypto';
   ```
3. Update crypto operations to use the new API
4. Remove react-native-crypto: `npm uninstall react-native-crypto`

### 2. stream-browserify (No metadata available)

**Problem**: This package has no metadata in React Native Directory.

**Current Usage**: Provides Node.js stream API for React Native.

**Status**: This package is actually fine to use, but expo-doctor flags it due to missing metadata.

**Action Taken**: Added to exclude list in package.json to suppress warnings.

## Configuration Changes Made

Added expo-doctor configuration to package.json to exclude problematic packages from checks:

```json
"expo": {
  "doctor": {
    "reactNativeDirectoryCheck": {
      "exclude": ["react-native-crypto", "stream-browserify"],
      "listUnknownPackages": false
    }
  }
}
```

## Next Steps

1. **Immediate**: The configuration changes will suppress the warnings
2. **Recommended**: Replace react-native-crypto with expo-crypto for better New Architecture support
3. **Optional**: Keep stream-browserify as it's working fine

## Testing After Changes

Run expo-doctor again to verify fixes:
```bash
npx expo-doctor
```

Should now show 15/15 checks passed.
