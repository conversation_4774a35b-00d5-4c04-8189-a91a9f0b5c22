# Java 17 Installation Guide

## The Problem
Your current Java version (1.8.0_391) is too old for the latest Android SDK Command Line Tools, which require Java 17 or higher.

## Solution: Install Java 17

### Option 1: Download Oracle JDK 17 (Recommended)
1. Go to: https://www.oracle.com/java/technologies/javase/jdk17-archive-downloads.html
2. Download "Windows x64 Installer" (jdk-17_windows-x64_bin.exe)
3. Run the installer
4. Follow the installation wizard

### Option 2: Download OpenJDK 17 (Free Alternative)
1. Go to: https://adoptium.net/temurin/releases/?version=17
2. Choose "Windows x64" and ".msi" installer
3. Download and install

### Option 3: Use Chocolatey (if you have it)
```powershell
choco install openjdk17
```

### Option 4: Use winget (Windows Package Manager)
```powershell
winget install Microsoft.OpenJDK.17
```

## After Installation

1. **Restart your terminal**
2. **Verify Java version:**
   ```bash
   java -version
   ```
   Should show version 17.x.x

3. **Run the Android SDK setup:**
   ```bash
   .\run-sdkmanager.bat --version
   ```

## Alternative: Use Older SDK Command Line Tools

If you prefer not to upgrade Java, you can download older command line tools that work with Java 8:

1. Go to: https://developer.android.com/studio#command-tools
2. Download an older version (like commandlinetools-win-6858069_latest.zip)
3. Replace your current command line tools

## Recommended: Install Java 17

Java 17 is the current LTS (Long Term Support) version and will be needed for modern Android development anyway.
