# Work Statistics Debug Guide

## Issue Description
Installers can click the "تەواو" (Completed) button on job cards, but the completed work is not appearing in the "ئاماری کارکردن" (Work Statistics) page.

## Debugging Steps Applied

### 1. Database Level Verification ✅
- **Trigger Function**: Confirmed working correctly
- **Work Statistics Table**: Records are being created automatically
- **RLS Policies**: Verified they allow proper data access
- **Test Data**: Successfully created test completion records

### 2. Frontend Debugging Added
- **Debug Logging**: Added console.log statements to track data flow
- **UI Debug Info**: Added debug text to show data counts and user info
- **Focus Refresh**: Added useFocusEffect to refresh data when screen comes into focus

### 3. Data Flow Verification
```
Job Completion → Database Trigger → Work Statistics Table → Frontend Query → UI Display
     ✅              ✅                    ✅                  ?              ?
```

## Current Debug Features

### InstallerJobsScreen Debug Logs
```javascript
console.log('InstallerJobsScreen: Updating job status', { jobId, status, installerRole })
console.log('InstallerJobsScreen: Adding completion tracking data', updateData)
console.log('InstallerJobsScreen: Job status updated successfully')
```

### WorkStatsScreen Debug Logs
```javascript
console.log('WorkStatsScreen: Fetching data for installer:', installerRole)
console.log('WorkStatsScreen: Fetched data:', data?.length || 0, 'records')
console.log('WorkStatsScreen: Sample data:', data?.[0])
```

### UI Debug Information
- Total data count vs filtered count
- Current user role
- Loading status

## Testing Instructions

### 1. Check Console Logs
When testing the app, monitor the console for these log messages:

1. **Job Completion Logs**:
   - "InstallerJobsScreen: Updating job status"
   - "InstallerJobsScreen: Adding completion tracking data"
   - "InstallerJobsScreen: Job status updated successfully"

2. **Work Stats Fetch Logs**:
   - "WorkStatsScreen: Fetching data for installer"
   - "WorkStatsScreen: Fetched data: X records"
   - "WorkStatsScreen: Sample data: {...}"

### 2. Check UI Debug Info
In the WorkStatsScreen, look for debug text showing:
- کۆی داتا: X | پاڵاوتنکراو: Y
- بەکارهێنەر: installer1/installer2 | بارکردن: بەڵێ/نەخێر

### 3. Database Verification
Run these queries to verify data flow:

```sql
-- Check recent job completions
SELECT id, customer_name, completion_status, completed_at, completed_by 
FROM jobs 
WHERE completion_status = 'completed' 
ORDER BY completed_at DESC LIMIT 5;

-- Check corresponding work_statistics
SELECT ws.*, j.customer_name as job_customer_name
FROM work_statistics ws
LEFT JOIN jobs j ON j.id = ws.job_id
ORDER BY ws.completion_date DESC LIMIT 5;
```

## Potential Issues to Investigate

### 1. Navigation/Tab Switching
- **Issue**: WorkStatsScreen might not be properly integrated in navigation
- **Check**: Verify the tab is accessible and properly configured
- **Solution**: Ensure InstallerTabs includes WorkStatsScreen correctly

### 2. User Role Mismatch
- **Issue**: User role in app might not match installer_id in database
- **Check**: Verify user.role matches the installer_id values in work_statistics
- **Solution**: Ensure consistent role naming (installer1/installer2)

### 3. Data Refresh Timing
- **Issue**: Screen might not refresh after job completion
- **Check**: Monitor if useFocusEffect triggers data refresh
- **Solution**: Added focus listener to refresh data when tab is accessed

### 4. RLS Policy Edge Cases
- **Issue**: Specific RLS policy conditions might block certain queries
- **Check**: Test with different user contexts
- **Solution**: Verify policies allow read access for authenticated users

### 5. Frontend State Management
- **Issue**: React state might not be updating properly
- **Check**: Monitor state changes in debug logs
- **Solution**: Verify setCompletedWork and setFilteredWork are called

## Quick Test Procedure

1. **Complete a Job**:
   - Go to InstallerJobsScreen
   - Click "تەواو" on a pending job
   - Check console for completion logs

2. **Check Work Statistics**:
   - Switch to WorkStatsScreen tab
   - Check console for fetch logs
   - Look at debug info in UI

3. **Verify Database**:
   - Check if work_statistics record was created
   - Verify installer_id matches user role

## Expected Behavior

### Successful Flow:
1. Installer clicks "تەواو" button
2. Console shows: "InstallerJobsScreen: Job status updated successfully"
3. Database trigger creates work_statistics record
4. User switches to WorkStatsScreen tab
5. Console shows: "WorkStatsScreen: Fetched data: X records"
6. UI shows completed work in the list

### Debug Information Should Show:
- کۆی داتا: > 0 (if there are completed jobs)
- بەکارهێنەر: installer1 or installer2
- بارکردن: نەخێر (when not loading)

## Next Steps Based on Findings

### If Console Shows No Fetch Logs:
- Navigation issue - WorkStatsScreen not being accessed
- Check tab configuration in AppNavigator

### If Fetch Logs Show 0 Records:
- Database query issue - check installer_id filtering
- Verify user role matches database values

### If Fetch Logs Show Records But UI Empty:
- React state issue - check setCompletedWork calls
- Verify FlatList data prop and renderItem function

### If Job Completion Logs Missing:
- InstallerJobsScreen issue - button not triggering update
- Check updateJobStatus function execution

This debug setup should help identify exactly where the data flow is breaking down.
