import React from 'react'
import { NavigationContainer } from '@react-navigation/native'
import { createStackNavigator } from '@react-navigation/stack'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { Ionicons } from '@expo/vector-icons'
import { I18nManager } from 'react-native'

import { useAuth } from '../contexts/AuthContext'
import LoginScreen from '../screens/LoginScreen'
import LoadingScreen from '../screens/LoadingScreen'

// Admin Screens
import AdminJobsScreen from '../screens/admin/AdminJobsScreen'
import AddCustomerScreen from '../screens/admin/AddCustomerScreen'
import UserManagementScreen from '../screens/admin/UserManagementScreen'
import AdminDashboardScreen from '../screens/admin/AdminDashboardScreen'

// Installer Screens
import InstallerJobsScreen from '../screens/installer/InstallerJobsScreen'
import InstallerStatsScreen from '../screens/installer/InstallerStatsScreen'

// Dispatch Screens
import DispatchJobsScreen from '../screens/dispatch/DispatchJobsScreen'

const Stack = createStackNavigator()
const Tab = createBottomTabNavigator()

// Enable RTL layout
I18nManager.forceRTL(true)

const AdminTabs = () => (
  <Tab.Navigator
    screenOptions={({ route }) => ({
      tabBarIcon: ({ focused, color, size }) => {
        let iconName: keyof typeof Ionicons.glyphMap

        if (route.name === 'Jobs') {
          iconName = focused ? 'list' : 'list-outline'
        } else if (route.name === 'AddCustomer') {
          iconName = focused ? 'add-circle' : 'add-circle-outline'
        } else if (route.name === 'UserManagement') {
          iconName = focused ? 'people' : 'people-outline'
        } else if (route.name === 'Dashboard') {
          iconName = focused ? 'stats-chart' : 'stats-chart-outline'
        } else {
          iconName = 'ellipse-outline'
        }

        return <Ionicons name={iconName} size={size} color={color} />
      },
      tabBarActiveTintColor: '#007AFF',
      tabBarInactiveTintColor: 'gray',
      headerShown: false,
    })}
  >
    <Tab.Screen 
      name="Jobs" 
      component={AdminJobsScreen} 
      options={{ tabBarLabel: 'ماڵەکان' }}
    />
    <Tab.Screen 
      name="AddCustomer" 
      component={AddCustomerScreen} 
      options={{ tabBarLabel: 'زیادکردن' }}
    />
    <Tab.Screen 
      name="UserManagement" 
      component={UserManagementScreen} 
      options={{ tabBarLabel: 'بەکارهێنەران' }}
    />
    <Tab.Screen 
      name="Dashboard" 
      component={AdminDashboardScreen} 
      options={{ tabBarLabel: 'داشبۆرد' }}
    />
  </Tab.Navigator>
)

const InstallerTabs = () => (
  <Tab.Navigator
    screenOptions={({ route }) => ({
      tabBarIcon: ({ focused, color, size }) => {
        let iconName: keyof typeof Ionicons.glyphMap

        if (route.name === 'Jobs') {
          iconName = focused ? 'list' : 'list-outline'
        } else if (route.name === 'Stats') {
          iconName = focused ? 'stats-chart' : 'stats-chart-outline'
        } else {
          iconName = 'ellipse-outline'
        }

        return <Ionicons name={iconName} size={size} color={color} />
      },
      tabBarActiveTintColor: '#007AFF',
      tabBarInactiveTintColor: 'gray',
      headerShown: false,
    })}
  >
    <Tab.Screen 
      name="Jobs" 
      component={InstallerJobsScreen} 
      options={{ tabBarLabel: 'کارەکان' }}
    />
    <Tab.Screen 
      name="Stats" 
      component={InstallerStatsScreen} 
      options={{ tabBarLabel: 'ئامار' }}
    />
  </Tab.Navigator>
)

const DispatchTabs = () => (
  <Tab.Navigator
    screenOptions={({ route }) => ({
      tabBarIcon: ({ focused, color, size }) => {
        let iconName: keyof typeof Ionicons.glyphMap

        if (route.name === 'Jobs') {
          iconName = focused ? 'list' : 'list-outline'
        } else {
          iconName = 'ellipse-outline'
        }

        return <Ionicons name={iconName} size={size} color={color} />
      },
      tabBarActiveTintColor: '#007AFF',
      tabBarInactiveTintColor: 'gray',
      headerShown: false,
    })}
  >
    <Tab.Screen
      name="Jobs"
      component={DispatchJobsScreen}
      options={{ tabBarLabel: 'کارەکان' }}
    />
  </Tab.Navigator>
)

const AppNavigator = () => {
  const { user, loading } = useAuth()

  if (loading) {
    return <LoadingScreen />
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {user ? (
          <>
            {user.role === 'admin' && (
              <Stack.Screen name="AdminTabs" component={AdminTabs} />
            )}
            {(user.role === 'installer1' || user.role === 'installer2') && (
              <Stack.Screen name="InstallerTabs" component={InstallerTabs} />
            )}
            {user.role === 'dispatch' && (
              <Stack.Screen name="DispatchTabs" component={DispatchTabs} />
            )}
          </>
        ) : (
          <Stack.Screen name="Login" component={LoginScreen} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  )
}

export default AppNavigator
