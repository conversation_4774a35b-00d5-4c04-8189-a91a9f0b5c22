/**
 * Test Adding Job with Images
 * This simulates adding a job with images to test the complete flow
 */

const { createClient } = require('@supabase/supabase-js')

// Your Supabase configuration
const SUPABASE_URL = 'https://emxbbmsieclwlslwgkua.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVteGJibXNpZWNsd2xzbHdna3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ3MzgsImV4cCI6MjA2MzkzMDczOH0.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

// Create a simple test image (1x1 pixel PNG)
function createTestImage() {
  const base64PNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=='
  return Buffer.from(base64PNG, 'base64')
}

async function testAddJobWithImages() {
  console.log('🧪 Testing Add Job with Images...\n')

  try {
    // Step 1: Upload test images
    console.log('1️⃣ Uploading test images...')
    
    const testImages = []
    const imageCount = 2
    
    for (let i = 0; i < imageCount; i++) {
      const testImageBuffer = createTestImage()
      const testFileName = `test-job-image-${Date.now()}-${i}.png`
      
      console.log(`  Uploading: ${testFileName}`)
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('job-images')
        .upload(testFileName, testImageBuffer, {
          contentType: 'image/png'
        })

      if (uploadError) {
        console.error(`  ❌ Upload failed: ${uploadError.message}`)
        return
      }
      
      // Get public URL
      const { data: urlData } = supabase.storage
        .from('job-images')
        .getPublicUrl(uploadData.path)
      
      testImages.push(urlData.publicUrl)
      console.log(`  ✅ Uploaded: ${urlData.publicUrl}`)
    }

    // Step 2: Get required data for job creation
    console.log('\n2️⃣ Getting required data...')
    
    // Get a place
    const { data: places, error: placesError } = await supabase
      .from('places')
      .select('*')
      .limit(1)

    if (placesError || !places.length) {
      console.error('❌ No places found')
      return
    }

    // Get an area
    const { data: areas, error: areasError } = await supabase
      .from('areas')
      .select('*')
      .limit(1)

    if (areasError || !areas.length) {
      console.error('❌ No areas found')
      return
    }

    // Get a referral source
    const { data: referralSources, error: referralError } = await supabase
      .from('referral_sources')
      .select('*')
      .limit(1)

    if (referralError || !referralSources.length) {
      console.error('❌ No referral sources found')
      return
    }

    // Get admin user
    const { data: adminUser, error: userError } = await supabase
      .from('app_users')
      .select('*')
      .eq('role', 'admin')
      .limit(1)

    if (userError || !adminUser.length) {
      console.error('❌ No admin user found')
      return
    }

    console.log('✅ Required data obtained')

    // Step 3: Create job with images
    console.log('\n3️⃣ Creating job with images...')
    
    const jobData = {
      date: new Date().toISOString().split('T')[0],
      customer_name: 'Test Customer with Images',
      customer_phone: '1234567890',
      place_id: places[0].id,
      area_id: areas[0].id,
      referral_source_id: referralSources[0].id,
      assigned_installer: 'installer1',
      job_status: 'not_ready',
      latitude: 36.1915,
      longitude: 44.0092,
      images: testImages, // This is the key part!
      created_by: adminUser[0].id,
    }

    console.log('Job data:', {
      customer_name: jobData.customer_name,
      images_count: jobData.images.length,
      images: jobData.images
    })

    const { data: newJob, error: jobError } = await supabase
      .from('jobs')
      .insert([jobData])
      .select()

    if (jobError) {
      console.error('❌ Failed to create job:', jobError.message)
      return
    }

    console.log('✅ Job created successfully!')
    console.log('  Job ID:', newJob[0].id)
    console.log('  Customer:', newJob[0].customer_name)
    console.log('  Images stored:', newJob[0].images?.length || 0)

    // Step 4: Verify the job was created with images
    console.log('\n4️⃣ Verifying job creation...')
    
    const { data: verifyJob, error: verifyError } = await supabase
      .from('jobs')
      .select('*')
      .eq('id', newJob[0].id)
      .single()

    if (verifyError) {
      console.error('❌ Failed to verify job:', verifyError.message)
      return
    }

    console.log('✅ Job verification:')
    console.log('  Customer:', verifyJob.customer_name)
    console.log('  Images count:', verifyJob.images?.length || 0)
    
    if (verifyJob.images && verifyJob.images.length > 0) {
      console.log('  Image URLs:')
      verifyJob.images.forEach((url, index) => {
        console.log(`    ${index + 1}. ${url}`)
      })
    }

    // Step 5: Test image accessibility
    console.log('\n5️⃣ Testing image accessibility...')
    
    if (verifyJob.images && verifyJob.images.length > 0) {
      for (let i = 0; i < verifyJob.images.length; i++) {
        const imageUrl = verifyJob.images[i]
        try {
          const response = await fetch(imageUrl)
          if (response.ok) {
            console.log(`  ✅ Image ${i + 1} is accessible (${response.status})`)
          } else {
            console.log(`  ❌ Image ${i + 1} not accessible (${response.status})`)
          }
        } catch (fetchError) {
          console.log(`  ❌ Image ${i + 1} fetch failed: ${fetchError.message}`)
        }
      }
    }

    console.log('\n🎉 SUCCESS!')
    console.log('✅ Job with images created and verified')
    console.log('✅ Images are accessible')
    console.log('✅ Your app should now show this job with image thumbnails')
    console.log('\n💡 Check your app now - you should see the new job with images!')

  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }
}

// Run the test
testAddJobWithImages()
