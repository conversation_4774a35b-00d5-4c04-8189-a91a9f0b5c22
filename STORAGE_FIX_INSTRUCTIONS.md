# Fix for Image Upload Error: "نەتوانرا بەشی وێنەکان دروست بکرێت"

## Problem Summary
Your app is failing to upload images because Supabase Storage has Row Level Security (RLS) enabled but no policies are configured to allow bucket creation and file uploads.

## Root Cause
- **Error**: `new row violates row-level security policy`
- **Location**: Supabase Storage `storage.buckets` table
- **Cause**: RLS enabled without proper policies

## Solution Steps

### Step 1: Access Supabase Dashboard
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Sign in to your account
3. Select your project: `<EMAIL>'s Project`

### Step 2: Open SQL Editor
1. In the left sidebar, click on **"SQL Editor"**
2. Click **"New query"** to create a new SQL script

### Step 3: Run the Fix SQL Commands
Copy and paste the entire content from `fix-storage-rls-policies.sql` into the SQL editor and click **"Run"**.

The SQL will:
- Create RLS policies for `storage.buckets` table
- Create RLS policies for `storage.objects` table  
- Create the `job-images` bucket
- Verify the setup

### Step 4: Verify the Fix
After running the SQL, you should see output showing the created policies.

### Step 5: Test the Fix
Run the diagnosis script again to verify everything works:

```bash
node test-storage-diagnosis.js
```

You should now see:
- ✅ Successfully listed buckets: ['job-images']
- ✅ job-images bucket already exists
- ✅ Successfully uploaded test file
- ✅ Generated public URL
- ✅ Successfully deleted test file
- 🎉 ALL TESTS PASSED!

## Alternative Solution (If SQL Doesn't Work)

If the SQL approach doesn't work, you can temporarily disable RLS:

### Option A: Disable RLS on Storage Tables
```sql
-- Temporarily disable RLS (less secure but will work)
ALTER TABLE storage.buckets DISABLE ROW LEVEL SECURITY;
ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;
```

### Option B: Create Bucket via Dashboard
1. Go to **Storage** in Supabase Dashboard
2. Click **"Create bucket"**
3. Name: `job-images`
4. Set as **Public bucket**: ✅
5. File size limit: `5242880` (5MB)
6. Allowed MIME types: `image/jpeg,image/png,image/webp,image/jpg`

## Security Notes

The implemented policies are secure because they:
- Only allow authenticated users to access storage
- Restrict access to the `job-images` bucket only
- Don't allow access to other buckets or system files

## Testing Your App

After applying the fix:

1. Open your app
2. Go to Add Customer screen
3. Try adding images
4. Save the job
5. The error should be resolved

## Troubleshooting

If you still get errors:

1. **Check Authentication**: Make sure you're logged in to the app
2. **Check Network**: Ensure stable internet connection
3. **Check File Size**: Ensure images are under 5MB
4. **Check File Format**: Use JPEG, PNG, or WebP images only

## Files Created for This Fix

- `test-storage-diagnosis.js` - Diagnosis script to identify issues
- `fix-storage-rls-policies.sql` - SQL commands to fix RLS policies
- `STORAGE_FIX_INSTRUCTIONS.md` - This instruction file

## Need Help?

If you encounter any issues:
1. Run the diagnosis script to see detailed error messages
2. Check the Supabase Dashboard logs
3. Verify your internet connection
4. Ensure you're using the correct Supabase project
