/**
 * Debug Loading Images Issue
 * Check why images are stuck in loading state
 */

const { createClient } = require('@supabase/supabase-js')

// Your Supabase configuration
const SUPABASE_URL = 'https://emxbbmsieclwlslwgkua.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVteGJibXNpZWNsd2xzbHdna3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ3MzgsImV4cCI6MjA2MzkzMDczOH0.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

async function debugLoadingImages() {
  console.log('🔍 Debugging Loading Images Issue...\n')

  try {
    // Get the job that has images but is showing loading
    console.log('1️⃣ Finding jobs with images...')
    
    const { data: jobs, error: jobsError } = await supabase
      .from('jobs')
      .select('*')
      .not('images', 'is', null)
      .order('created_at', { ascending: false })
      .limit(5)

    if (jobsError) {
      console.error('❌ Failed to fetch jobs:', jobsError.message)
      return
    }

    console.log(`✅ Found ${jobs.length} jobs with images`)

    // Check each job's images
    for (const job of jobs) {
      console.log(`\n📋 Job: ${job.customer_name} (ID: ${job.id})`)
      console.log(`   Images count: ${job.images?.length || 0}`)
      
      if (job.images && job.images.length > 0) {
        for (let i = 0; i < job.images.length; i++) {
          const imageUrl = job.images[i]
          console.log(`\n   🖼️ Image ${i + 1}: ${imageUrl}`)
          
          if (!imageUrl || imageUrl === 'null' || imageUrl === 'undefined') {
            console.log('   ❌ Invalid URL (null/undefined)')
            continue
          }
          
          try {
            console.log('   Testing accessibility...')
            const response = await fetch(imageUrl, {
              method: 'HEAD', // Just check headers, don't download
              timeout: 10000 // 10 second timeout
            })
            
            console.log(`   Status: ${response.status}`)
            console.log(`   Content-Type: ${response.headers.get('content-type')}`)
            console.log(`   Content-Length: ${response.headers.get('content-length')} bytes`)
            console.log(`   Accessible: ${response.ok ? '✅' : '❌'}`)
            
            if (!response.ok) {
              console.log(`   ❌ HTTP Error: ${response.status} ${response.statusText}`)
              
              // Try to get error details
              try {
                const errorText = await response.text()
                console.log(`   Error details: ${errorText}`)
              } catch (e) {
                console.log('   Could not read error details')
              }
            }
            
          } catch (fetchError) {
            console.log(`   ❌ Fetch failed: ${fetchError.message}`)
            
            if (fetchError.message.includes('timeout')) {
              console.log('   💡 Image request timed out - server might be slow')
            } else if (fetchError.message.includes('network')) {
              console.log('   💡 Network error - check internet connection')
            } else if (fetchError.message.includes('CORS')) {
              console.log('   💡 CORS error - might be a browser security issue')
            }
          }
        }
      }
    }

    // Test storage bucket accessibility
    console.log('\n2️⃣ Testing storage bucket...')
    
    const { data: files, error: listError } = await supabase.storage
      .from('job-images')
      .list()

    if (listError) {
      console.log('❌ Cannot access storage bucket:', listError.message)
    } else {
      console.log(`✅ Storage bucket accessible with ${files.length} files`)
      
      // Test a few file URLs
      console.log('\n3️⃣ Testing storage file URLs...')
      
      for (let i = 0; i < Math.min(3, files.length); i++) {
        const file = files[i]
        const { data: urlData } = supabase.storage
          .from('job-images')
          .getPublicUrl(file.name)
        
        const testUrl = urlData.publicURL || urlData.publicUrl
        console.log(`\n   File: ${file.name}`)
        console.log(`   URL: ${testUrl}`)
        
        if (testUrl) {
          try {
            const response = await fetch(testUrl, { method: 'HEAD' })
            console.log(`   Status: ${response.status}`)
            console.log(`   Accessible: ${response.ok ? '✅' : '❌'}`)
          } catch (e) {
            console.log(`   ❌ Fetch failed: ${e.message}`)
          }
        }
      }
    }

    // Check if there are any CORS or security issues
    console.log('\n4️⃣ Checking for common issues...')
    
    console.log('Checking bucket configuration...')
    const { data: buckets } = await supabase.storage.listBuckets()
    const jobImagesBucket = buckets?.find(b => b.name === 'job-images')
    
    if (jobImagesBucket) {
      console.log('✅ Bucket configuration:')
      console.log(`   Public: ${jobImagesBucket.public}`)
      console.log(`   File size limit: ${jobImagesBucket.file_size_limit}`)
      console.log(`   Allowed MIME types: ${jobImagesBucket.allowed_mime_types}`)
      
      if (!jobImagesBucket.public) {
        console.log('❌ ISSUE: Bucket is not public!')
        console.log('💡 This could cause images to fail loading')
      }
    }

    console.log('\n📋 RECOMMENDATIONS:')
    
    // Check if we found any working images
    let foundWorkingImages = false
    for (const job of jobs) {
      if (job.images && job.images.length > 0) {
        for (const imageUrl of job.images) {
          if (imageUrl && imageUrl !== 'null' && imageUrl !== 'undefined') {
            try {
              const response = await fetch(imageUrl, { method: 'HEAD' })
              if (response.ok) {
                foundWorkingImages = true
                break
              }
            } catch (e) {
              // Continue checking
            }
          }
        }
        if (foundWorkingImages) break
      }
    }
    
    if (foundWorkingImages) {
      console.log('✅ Some images are working - issue might be intermittent')
      console.log('💡 Try refreshing the app or checking internet connection')
    } else {
      console.log('❌ No images are accessible')
      console.log('💡 Possible issues:')
      console.log('   - Storage bucket permissions')
      console.log('   - Network connectivity')
      console.log('   - Invalid URLs in database')
      console.log('   - CORS configuration')
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }
}

// Run the debug
debugLoadingImages()
