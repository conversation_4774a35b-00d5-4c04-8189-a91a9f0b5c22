/**
 * Test Specific Image URL
 * Test the exact image that's showing loading in your app
 */

async function testSpecificImage() {
  console.log('🖼️ Testing Specific Image URL...\n')

  // This is the image URL from the debug output for the "Jsjs" job
  const imageUrl = 'https://emxbbmsieclwlslwgkua.supabase.co/storage/v1/object/public/job-images/1749568400795_ex2g5yx9trv.jpeg'
  
  console.log('Testing URL:', imageUrl)
  
  try {
    console.log('\n1️⃣ Testing basic fetch...')
    const response = await fetch(imageUrl)
    console.log('Status:', response.status)
    console.log('Status Text:', response.statusText)
    console.log('Content-Type:', response.headers.get('content-type'))
    console.log('Content-Length:', response.headers.get('content-length'))
    console.log('Cache-Control:', response.headers.get('cache-control'))
    console.log('Access-Control-Allow-Origin:', response.headers.get('access-control-allow-origin'))
    
    if (response.ok) {
      console.log('✅ Image is accessible via fetch')
      
      // Test if we can read the data
      console.log('\n2️⃣ Testing data reading...')
      const arrayBuffer = await response.arrayBuffer()
      console.log('✅ Image data readable, size:', arrayBuffer.byteLength, 'bytes')
      
    } else {
      console.log('❌ Image not accessible')
      const errorText = await response.text()
      console.log('Error response:', errorText)
    }
    
  } catch (error) {
    console.log('❌ Fetch failed:', error.message)
  }
  
  // Test with different parameters
  console.log('\n3️⃣ Testing with cache-busting parameter...')
  const cacheBustUrl = `${imageUrl}?t=${Date.now()}`
  
  try {
    const response2 = await fetch(cacheBustUrl)
    console.log('Cache-bust Status:', response2.status)
    console.log('Cache-bust accessible:', response2.ok ? '✅' : '❌')
  } catch (error) {
    console.log('❌ Cache-bust fetch failed:', error.message)
  }
  
  // Test CORS
  console.log('\n4️⃣ Testing CORS...')
  try {
    const response3 = await fetch(imageUrl, {
      method: 'HEAD',
      mode: 'cors'
    })
    console.log('CORS test status:', response3.status)
    console.log('CORS accessible:', response3.ok ? '✅' : '❌')
  } catch (error) {
    console.log('❌ CORS test failed:', error.message)
  }
  
  console.log('\n📋 RECOMMENDATIONS:')
  console.log('1. Try refreshing your app completely (close and reopen)')
  console.log('2. Check if you have a stable internet connection')
  console.log('3. The image URL is working, so the issue might be in the React Native Image component')
  console.log('4. Try clearing the app cache if possible')
  console.log('5. The improved EnhancedImage component should help with timeouts')
}

// Run the test
testSpecificImage()
