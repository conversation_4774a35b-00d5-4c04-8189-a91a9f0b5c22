# Supabase Integration Setup

This project is configured to work with Supabase for backend services including authentication, database, and real-time features.

## Configuration

### Environment Variables

1. Copy `.env.example` to `.env`
2. Fill in your Supabase project details:
   - `EXPO_PUBLIC_SUPABASE_URL`: Your Supabase project URL
   - `EXPO_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key

### Project Details

- **Project ID**: emxbbmsieclwlslwgkua
- **Region**: eu-central-1
- **Database Host**: db.emxbbmsieclwlslwgkua.supabase.co

## Features Included

### Authentication
- Sign up with email/password
- Sign in with email/password
- Sign out
- Session management
- Auth state persistence

### File Structure

```
lib/
  supabase.ts          # Supabase client configuration
components/
  SupabaseExample.tsx  # Example authentication component
types/
  supabase.ts          # TypeScript type definitions
```

## Usage

The app includes a complete authentication example in `components/SupabaseExample.tsx` that demonstrates:

- User registration
- User login
- Session management
- Sign out functionality

## Security Notes

- The anonymous key is safe to use in client-side code
- Never expose the service role key in client-side code
- Row Level Security (RLS) should be enabled on your Supabase tables
- Environment variables are properly configured for Expo

## Next Steps

1. **Enable Authentication** in your Supabase dashboard
2. **Create tables** in your database
3. **Set up Row Level Security** policies
4. **Update TypeScript types** in `types/supabase.ts` to match your database schema

## Testing

Run your app with:
```bash
npm start
```

Then test the authentication flow using the example component.
