import React, { useState } from 'react'
import { 
  Image, 
  View, 
  Text, 
  StyleSheet, 
  ActivityIndicator, 
  TouchableOpacity,
  ImageStyle,
  ViewStyle
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'

interface SimpleImageProps {
  source: { uri: string }
  style?: ImageStyle
  containerStyle?: ViewStyle
  onPress?: () => void
}

const SimpleImage: React.FC<SimpleImageProps> = ({
  source,
  style,
  containerStyle,
  onPress
}) => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)

  const handleLoadStart = () => {
    console.log('🔄 SimpleImage: Load STARTED -', source.uri)
    setLoading(true)
    setError(false)
  }

  const handleLoad = () => {
    console.log('✅ SimpleImage: Load SUCCESS -', source.uri)
    setLoading(false)
    setError(false)
  }

  const handleLoadEnd = () => {
    console.log('🏁 SimpleImage: Load ENDED -', source.uri)
    // Don't change loading state here, let onLoad handle it
  }

  const handleError = (errorEvent: any) => {
    console.log('❌ SimpleImage: Load ERROR -', source.uri, errorEvent.nativeEvent)
    setLoading(false)
    setError(true)
  }

  const renderContent = () => {
    if (loading && !error) {
      return (
        <View style={[styles.placeholderContainer, style]}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.loadingText}>بارکردن...</Text>
        </View>
      )
    }

    if (error) {
      return (
        <View style={[styles.errorContainer, style]}>
          <Ionicons name="image-outline" size={24} color="#999" />
          <Text style={styles.errorText}>نەتوانرا وێنەکە بار بکرێت</Text>
        </View>
      )
    }

    return (
      <Image
        source={{ uri: source.uri }}
        style={style}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onLoad={handleLoad}
        onError={handleError}
        resizeMode="cover"
      />
    )
  }

  const content = renderContent()

  if (onPress) {
    return (
      <TouchableOpacity 
        style={[styles.container, containerStyle]} 
        onPress={onPress}
        disabled={loading || error}
      >
        {content}
      </TouchableOpacity>
    )
  }

  return (
    <View style={[styles.container, containerStyle]}>
      {content}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  placeholderContainer: {
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#f8f8f8',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  errorText: {
    marginTop: 8,
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
})

export default SimpleImage
