import React, { useState, useEffect } from 'react'
import { 
  Image, 
  View, 
  Text, 
  StyleSheet, 
  ActivityIndicator, 
  TouchableOpacity,
  ImageStyle,
  ViewStyle
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'

interface PreloadImageProps {
  source: { uri: string }
  style?: ImageStyle
  containerStyle?: ViewStyle
  onPress?: () => void
}

const PreloadImage: React.FC<PreloadImageProps> = ({
  source,
  style,
  containerStyle,
  onPress
}) => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const [preloaded, setPreloaded] = useState(false)

  useEffect(() => {
    console.log('🔄 PreloadImage: Starting preload for:', source.uri)
    setLoading(true)
    setError(false)
    setPreloaded(false)

    // Preload the image using Image.prefetch
    const preloadImage = async () => {
      try {
        console.log('📥 PreloadImage: Attempting to prefetch...')
        await Image.prefetch(source.uri)
        console.log('✅ PreloadImage: Prefetch successful!')
        setPreloaded(true)
        setLoading(false)
        setError(false)
      } catch (prefetchError) {
        console.log('❌ PreloadImage: Prefetch failed:', prefetchError)
        // Try direct loading as fallback
        setPreloaded(true) // Still try to show the image
        setLoading(false)
        setError(false)
      }
    }

    preloadImage()
  }, [source.uri])

  const handleLoad = () => {
    console.log('✅ PreloadImage: Image rendered successfully')
    setLoading(false)
    setError(false)
  }

  const handleError = (errorEvent: any) => {
    console.log('❌ PreloadImage: Render error:', errorEvent.nativeEvent)
    setLoading(false)
    setError(true)
  }

  const renderContent = () => {
    if (loading) {
      return (
        <View style={[styles.placeholderContainer, style]}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.loadingText}>پێش‌بارکردن...</Text>
        </View>
      )
    }

    if (error) {
      return (
        <View style={[styles.errorContainer, style]}>
          <Ionicons name="image-outline" size={20} color="#999" />
          <Text style={styles.errorText}>هەڵە</Text>
        </View>
      )
    }

    return (
      <Image
        source={{ uri: source.uri }}
        style={style}
        onLoad={handleLoad}
        onError={handleError}
        resizeMode="cover"
      />
    )
  }

  const content = renderContent()

  if (onPress) {
    return (
      <TouchableOpacity 
        style={[styles.container, containerStyle]} 
        onPress={onPress}
        disabled={loading || error}
      >
        {content}
      </TouchableOpacity>
    )
  }

  return (
    <View style={[styles.container, containerStyle]}>
      {content}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  placeholderContainer: {
    backgroundColor: '#e8f4fd',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: '#007AFF',
    borderStyle: 'dashed',
  },
  loadingText: {
    marginTop: 4,
    fontSize: 10,
    color: '#007AFF',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  errorContainer: {
    backgroundColor: '#fdf2f2',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: '#ff4444',
    borderStyle: 'dashed',
  },
  errorText: {
    marginTop: 4,
    fontSize: 10,
    color: '#ff4444',
    textAlign: 'center',
    fontWeight: 'bold',
  },
})

export default PreloadImage
