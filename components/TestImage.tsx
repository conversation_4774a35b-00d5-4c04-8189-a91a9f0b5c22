import React, { useState, useEffect } from 'react'
import { 
  Image, 
  View, 
  Text, 
  StyleSheet, 
  ActivityIndicator, 
  TouchableOpacity,
  ImageStyle,
  ViewStyle
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'

interface TestImageProps {
  source: { uri: string }
  style?: ImageStyle
  containerStyle?: ViewStyle
  onPress?: () => void
  testMode?: 'basic' | 'cached' | 'headers' | 'timeout'
}

const TestImage: React.FC<TestImageProps> = ({
  source,
  style,
  containerStyle,
  onPress,
  testMode = 'basic'
}) => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const [testResult, setTestResult] = useState('')

  useEffect(() => {
    console.log(`🧪 TestImage (${testMode}): Starting test with URL:`, source.uri)
    setLoading(true)
    setError(false)
    setTestResult('')
  }, [source.uri, testMode])

  const handleLoadStart = () => {
    console.log(`🔄 TestImage (${testMode}): onLoadStart fired`)
    setTestResult('LoadStart fired')
  }

  const handleLoad = () => {
    console.log(`✅ TestImage (${testMode}): onLoad fired - SUCCESS!`)
    setLoading(false)
    setError(false)
    setTestResult('Load SUCCESS!')
  }

  const handleLoadEnd = () => {
    console.log(`🏁 TestImage (${testMode}): onLoadEnd fired`)
    setTestResult(prev => prev + ' + LoadEnd')
  }

  const handleError = (errorEvent: any) => {
    console.log(`❌ TestImage (${testMode}): onError fired:`, errorEvent.nativeEvent)
    setLoading(false)
    setError(true)
    setTestResult('ERROR: ' + JSON.stringify(errorEvent.nativeEvent))
  }

  const getImageSource = () => {
    const baseUri = source.uri
    
    switch (testMode) {
      case 'cached':
        return { uri: `${baseUri}?cache=reload` }
      case 'headers':
        return { 
          uri: baseUri,
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        }
      case 'timeout':
        return { uri: `${baseUri}?t=${Date.now()}` }
      default:
        return { uri: baseUri }
    }
  }

  const renderContent = () => {
    if (loading && !error) {
      return (
        <View style={[styles.placeholderContainer, style]}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.loadingText}>Testing {testMode}...</Text>
          <Text style={styles.statusText}>{testResult}</Text>
        </View>
      )
    }

    if (error) {
      return (
        <View style={[styles.errorContainer, style]}>
          <Ionicons name="warning" size={20} color="#ff4444" />
          <Text style={styles.errorText}>Test {testMode} failed</Text>
          <Text style={styles.statusText}>{testResult}</Text>
        </View>
      )
    }

    return (
      <View style={style}>
        <Image
          source={getImageSource()}
          style={style}
          onLoadStart={handleLoadStart}
          onLoadEnd={handleLoadEnd}
          onLoad={handleLoad}
          onError={handleError}
          resizeMode="cover"
        />
        <View style={styles.successOverlay}>
          <Text style={styles.successText}>✅ {testMode}</Text>
        </View>
      </View>
    )
  }

  const content = renderContent()

  if (onPress) {
    return (
      <TouchableOpacity 
        style={[styles.container, containerStyle]} 
        onPress={onPress}
      >
        {content}
      </TouchableOpacity>
    )
  }

  return (
    <View style={[styles.container, containerStyle]}>
      {content}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    position: 'relative',
  },
  placeholderContainer: {
    backgroundColor: '#f0f8ff',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    borderWidth: 1,
    borderColor: '#007AFF',
    borderStyle: 'dashed',
  },
  loadingText: {
    marginTop: 4,
    fontSize: 10,
    color: '#007AFF',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  statusText: {
    marginTop: 2,
    fontSize: 8,
    color: '#666',
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#fff0f0',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
    borderWidth: 1,
    borderColor: '#ff4444',
    borderStyle: 'dashed',
  },
  errorText: {
    marginTop: 4,
    fontSize: 10,
    color: '#ff4444',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  successOverlay: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: 'rgba(0, 255, 0, 0.8)',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  successText: {
    fontSize: 8,
    color: 'white',
    fontWeight: 'bold',
  },
})

export default TestImage
