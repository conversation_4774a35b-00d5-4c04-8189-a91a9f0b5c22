import React from 'react'
import { View, StyleSheet } from 'react-native'
import { WebView } from 'react-native-webview'

interface LeafletMapProps {
  latitude: number
  longitude: number
  height?: number
  onLocationChange?: (lat: number, lng: number) => void
  interactive?: boolean
  showAccuracy?: boolean
  accuracyRadius?: number
  showCurrentLocationButton?: boolean
}

const LeafletMap: React.FC<LeafletMapProps> = ({
  latitude,
  longitude,
  height = 200,
  onLocationChange,
  interactive = false,
  showAccuracy = false,
  accuracyRadius = 0,
  showCurrentLocationButton = false
}) => {
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Map</title>
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
        <style>
            body { margin: 0; padding: 0; }
            #map { height: 100vh; width: 100%; }
            .leaflet-control-attribution { display: none !important; }
        </style>
    </head>
    <body>
        <div id="map"></div>
        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
        <script>
            // Initialize map
            var map = L.map('map', {
                center: [${latitude}, ${longitude}],
                zoom: 15,
                zoomControl: true,
                scrollWheelZoom: ${interactive},
                doubleClickZoom: ${interactive},
                boxZoom: ${interactive},
                keyboard: ${interactive},
                dragging: ${interactive},
                touchZoom: ${interactive}
            });

            // Add multiple tile layer options for better coverage
            var osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 19
            });

            var satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: '© Esri, DigitalGlobe, GeoEye, Earthstar Geographics, CNES/Airbus DS, USDA, USGS, AeroGRID, IGN, and the GIS User Community',
                maxZoom: 19
            });

            // Add default layer
            osmLayer.addTo(map);

            // Add layer control
            var baseLayers = {
                "خەریتەی ئاسایی": osmLayer,
                "خەریتەی دەریایی": satelliteLayer
            };
            L.control.layers(baseLayers).addTo(map);

            // Add accuracy circle if provided
            ${showAccuracy && accuracyRadius > 0 ? `
            var accuracyCircle = L.circle([${latitude}, ${longitude}], {
                radius: ${accuracyRadius},
                color: '#007AFF',
                fillColor: '#007AFF',
                fillOpacity: 0.1,
                weight: 2,
                dashArray: '5, 5'
            }).addTo(map);
            ` : ''}

            // Add marker
            var marker = L.marker([${latitude}, ${longitude}]).addTo(map);

            // Enhanced custom marker icon
            var customIcon = L.divIcon({
                className: 'custom-marker',
                html: '<div style="background-color: #FF3B30; width: 24px; height: 24px; border-radius: 50%; border: 4px solid white; box-shadow: 0 3px 6px rgba(0,0,0,0.4); position: relative;"><div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 8px; height: 8px; background-color: white; border-radius: 50%;"></div></div>',
                iconSize: [24, 24],
                iconAnchor: [12, 12]
            });

            marker.setIcon(customIcon);

            ${interactive ? `
            // Make marker draggable if interactive
            marker.setDraggable(true);

            marker.on('dragend', function(e) {
                var position = marker.getLatLng();

                // Update accuracy circle if it exists
                ${showAccuracy && accuracyRadius > 0 ? `
                if (accuracyCircle) {
                    accuracyCircle.setLatLng(position);
                }
                ` : ''}

                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'locationChange',
                    latitude: position.lat,
                    longitude: position.lng
                }));
            });

            // Handle map clicks
            map.on('click', function(e) {
                marker.setLatLng(e.latlng);

                // Update accuracy circle if it exists
                ${showAccuracy && accuracyRadius > 0 ? `
                if (accuracyCircle) {
                    accuracyCircle.setLatLng(e.latlng);
                }
                ` : ''}

                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'locationChange',
                    latitude: e.latlng.lat,
                    longitude: e.latlng.lng
                }));
            });
            ` : ''}

            ${showCurrentLocationButton ? `
            // Add current location button
            L.Control.CurrentLocation = L.Control.extend({
                onAdd: function(map) {
                    var container = L.DomUtil.create('div', 'leaflet-bar leaflet-control leaflet-control-custom');
                    container.style.backgroundColor = 'white';
                    container.style.width = '40px';
                    container.style.height = '40px';
                    container.style.cursor = 'pointer';
                    container.innerHTML = '<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-size: 18px;">📍</div>';

                    container.onclick = function(){
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'getCurrentLocation'
                        }));
                    }

                    return container;
                }
            });

            new L.Control.CurrentLocation({ position: 'topright' }).addTo(map);
            ` : ''}

            // Fit map to show marker with better zoom level
            map.setView([${latitude}, ${longitude}], 16);
        </script>
    </body>
    </html>
  `

  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data)

      if (data.type === 'locationChange' && onLocationChange) {
        onLocationChange(data.latitude, data.longitude)
      } else if (data.type === 'getCurrentLocation') {
        // Handle current location request from map button
        // This could trigger a callback to parent component
        console.log('Current location requested from map')
      }
    } catch (error) {
      console.error('Error parsing map message:', error)
    }
  }

  return (
    <View style={[styles.container, { height }]}>
      <WebView
        source={{ html: htmlContent }}
        style={styles.webview}
        onMessage={handleMessage}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        scalesPageToFit={true}
        scrollEnabled={false}
        bounces={false}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#f0f0f0',
  },
  webview: {
    flex: 1,
  },
})

export default LeafletMap
