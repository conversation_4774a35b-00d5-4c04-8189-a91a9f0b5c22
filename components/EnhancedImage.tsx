import React, { useState, useEffect } from 'react'
import { 
  Image, 
  View, 
  Text, 
  StyleSheet, 
  ActivityIndicator, 
  TouchableOpacity,
  ImageStyle,
  ViewStyle,
  TextStyle
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'

interface EnhancedImageProps {
  source: { uri: string }
  style?: ImageStyle
  containerStyle?: ViewStyle
  onPress?: () => void
  showRetry?: boolean
  placeholder?: React.ReactNode
}

const EnhancedImage: React.FC<EnhancedImageProps> = ({
  source,
  style,
  containerStyle,
  onPress,
  showRetry = true,
  placeholder
}) => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null)

  const clearLoadingTimeout = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
      setTimeoutId(null)
    }
  }

  const handleLoadStart = () => {
    console.log('Image load started:', source.uri)
    setLoading(true)
    setError(false)

    // Set a timeout to prevent infinite loading
    clearLoadingTimeout()
    const newTimeoutId = setTimeout(() => {
      console.log('Image load timeout after 20 seconds:', source.uri)
      setLoading(false)
      setError(true)
    }, 20000) // Increased to 20 second timeout for large images
    setTimeoutId(newTimeoutId)
  }

  const handleLoadEnd = () => {
    console.log('Image load ended:', source.uri)
    clearLoadingTimeout()
    setLoading(false)
  }

  const handleLoad = () => {
    console.log('Image loaded successfully:', source.uri)
    clearLoadingTimeout()
    setLoading(false)
    setError(false)
  }

  const handleError = (errorEvent: any) => {
    console.log('Image load error:', source.uri, errorEvent.nativeEvent?.error)
    clearLoadingTimeout()
    setLoading(false)
    setError(true)
  }

  const handleRetry = () => {
    if (retryCount < 3) {
      console.log('Retrying image load:', source.uri, 'attempt:', retryCount + 1)
      setRetryCount(prev => prev + 1)
      setError(false)
      setLoading(true)
    }
  }

  // Reset state when source URI changes
  useEffect(() => {
    setLoading(true)
    setError(false)
    setRetryCount(0)
    clearLoadingTimeout()
  }, [source.uri])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      clearLoadingTimeout()
    }
  }, [])

  const renderContent = () => {
    if (loading) {
      return (
        <View style={[styles.placeholderContainer, style]}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.loadingText}>بارکردن...</Text>
        </View>
      )
    }

    if (error) {
      return (
        <View style={[styles.errorContainer, style]}>
          <Ionicons name="image-outline" size={24} color="#999" />
          <Text style={styles.errorText}>نەتوانرا وێنەکە بار بکرێت</Text>
          {showRetry && retryCount < 3 && (
            <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
              <Ionicons name="refresh" size={16} color="#007AFF" />
              <Text style={styles.retryText}>دووبارە هەوڵ بدەرەوە ({retryCount + 1}/3)</Text>
            </TouchableOpacity>
          )}
          {retryCount >= 3 && (
            <Text style={styles.maxRetriesText}>زۆرترین هەوڵدان تەواو بوو</Text>
          )}
        </View>
      )
    }

    return (
      <Image
        source={{
          uri: retryCount > 0 ? `${source.uri}?cache=${Date.now()}&retry=${retryCount}` : `${source.uri}?cache=${Date.now()}`,
          cache: 'reload'
        }}
        style={style}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onLoad={handleLoad}
        onError={handleError}
        resizeMode="cover"
        fadeDuration={300}
      />
    )
  }

  const content = renderContent()

  if (onPress) {
    return (
      <TouchableOpacity 
        style={[styles.container, containerStyle]} 
        onPress={onPress}
        disabled={loading || error}
      >
        {content}
      </TouchableOpacity>
    )
  }

  return (
    <View style={[styles.container, containerStyle]}>
      {content}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  placeholderContainer: {
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: '#f8f8f8',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  errorText: {
    marginTop: 8,
    fontSize: 12,
    color: '#999',
    textAlign: 'center',
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#f0f8ff',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  retryText: {
    marginLeft: 4,
    fontSize: 11,
    color: '#007AFF',
    fontWeight: '500',
  },
  maxRetriesText: {
    marginTop: 8,
    fontSize: 10,
    color: '#999',
    textAlign: 'center',
  },
})

export default EnhancedImage
