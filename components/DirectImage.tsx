import React, { useState, useEffect } from 'react'
import { 
  Image, 
  View, 
  Text, 
  StyleSheet, 
  ActivityIndicator, 
  TouchableOpacity,
  ImageStyle,
  ViewStyle
} from 'react-native'
import { Ionicons } from '@expo/vector-icons'

interface DirectImageProps {
  source: { uri: string }
  style?: ImageStyle
  containerStyle?: ViewStyle
  onPress?: () => void
}

const DirectImage: React.FC<DirectImageProps> = ({
  source,
  style,
  containerStyle,
  onPress
}) => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)

  // Force loading state to false after a reasonable time
  useEffect(() => {
    console.log('🚀 DirectImage: Starting with URL:', source.uri)
    setLoading(true)
    setError(false)
    setImageLoaded(false)

    // Force load completion after 3 seconds if events don't fire
    const forceLoadTimer = setTimeout(() => {
      console.log('⏰ DirectImage: Force completing load after 3 seconds')
      setLoading(false)
      setImageLoaded(true)
    }, 3000)

    return () => clearTimeout(forceLoadTimer)
  }, [source.uri])

  const handleLoadStart = () => {
    console.log('🔄 DirectImage: onLoadStart')
  }

  const handleLoad = () => {
    console.log('✅ DirectImage: onLoad - Image loaded successfully!')
    setLoading(false)
    setError(false)
    setImageLoaded(true)
  }

  const handleError = (errorEvent: any) => {
    console.log('❌ DirectImage: onError:', errorEvent.nativeEvent)
    setLoading(false)
    setError(true)
    setImageLoaded(false)
  }

  const renderContent = () => {
    // Always render the image, but overlay loading/error states
    return (
      <View style={[styles.container, containerStyle]}>
        {/* Always render the image */}
        <Image
          source={{ uri: source.uri }}
          style={style}
          onLoadStart={handleLoadStart}
          onLoad={handleLoad}
          onError={handleError}
          resizeMode="cover"
        />
        
        {/* Overlay loading state */}
        {loading && !imageLoaded && (
          <View style={[styles.overlay, styles.loadingOverlay]}>
            <ActivityIndicator size="small" color="#007AFF" />
            <Text style={styles.overlayText}>بارکردن...</Text>
          </View>
        )}
        
        {/* Overlay error state */}
        {error && (
          <View style={[styles.overlay, styles.errorOverlay]}>
            <Ionicons name="image-outline" size={20} color="#999" />
            <Text style={styles.overlayText}>خراپ</Text>
          </View>
        )}
        
        {/* Success indicator */}
        {imageLoaded && !loading && !error && (
          <View style={styles.successIndicator}>
            <Text style={styles.successText}>✓</Text>
          </View>
        )}
      </View>
    )
  }

  const content = renderContent()

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} disabled={loading || error}>
        {content}
      </TouchableOpacity>
    )
  }

  return content
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    overflow: 'hidden',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingOverlay: {
    backgroundColor: 'rgba(245, 245, 245, 0.9)',
  },
  errorOverlay: {
    backgroundColor: 'rgba(248, 248, 248, 0.9)',
  },
  overlayText: {
    marginTop: 4,
    fontSize: 10,
    color: '#666',
    textAlign: 'center',
  },
  successIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: 'rgba(16, 185, 129, 0.9)',
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  successText: {
    fontSize: 10,
    color: 'white',
    fontWeight: 'bold',
  },
})

export default DirectImage
