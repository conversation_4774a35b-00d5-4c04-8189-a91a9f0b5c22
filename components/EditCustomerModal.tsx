import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  Alert,
} from 'react-native'
import { Picker } from '@react-native-picker/picker'
import { Ionicons } from '@expo/vector-icons'
import { supabase } from '../lib/supabase'

interface Place {
  id: string
  name: string
}

interface Area {
  id: string
  name: string
  place_id: string
}

interface ReferralSource {
  id: string
  name: string
}

interface Job {
  id: string
  customer_name: string
  customer_phone: string
  place_id?: string
  area_id?: string
  referral_source_id?: string
  assigned_installer?: string
  place_name?: string
  area_name?: string
  referral_source_name?: string
}

interface EditCustomerModalProps {
  visible: boolean
  job: Job | null
  onClose: () => void
  onSave: () => void
}

const EditCustomerModal: React.FC<EditCustomerModalProps> = ({
  visible,
  job,
  onClose,
  onSave,
}) => {
  const [customerName, setCustomerName] = useState('')
  const [customerPhone, setCustomerPhone] = useState('')
  const [places, setPlaces] = useState<Place[]>([])
  const [areas, setAreas] = useState<Area[]>([])
  const [referralSources, setReferralSources] = useState<ReferralSource[]>([])
  const [selectedPlace, setSelectedPlace] = useState('')
  const [selectedArea, setSelectedArea] = useState('')
  const [selectedReferralSource, setSelectedReferralSource] = useState('')
  const [assignedInstaller, setAssignedInstaller] = useState('installer1')
  const [loading, setLoading] = useState(false)
  const [showAddPlace, setShowAddPlace] = useState(false)
  const [showAddArea, setShowAddArea] = useState(false)
  const [showAddReferralSource, setShowAddReferralSource] = useState(false)
  const [newPlaceName, setNewPlaceName] = useState('')
  const [newAreaName, setNewAreaName] = useState('')
  const [newReferralSourceName, setNewReferralSourceName] = useState('')

  useEffect(() => {
    if (visible && job) {
      // Pre-populate form with current job data
      setCustomerName(job.customer_name || '')
      setCustomerPhone(job.customer_phone || '')
      setSelectedPlace(job.place_id || '')
      setSelectedArea(job.area_id || '')
      setSelectedReferralSource(job.referral_source_id || '')
      setAssignedInstaller(job.assigned_installer || 'installer1')

      fetchPlaces()
      fetchReferralSources()

      if (job.place_id) {
        fetchAreas(job.place_id)
      }
    }
  }, [visible, job])

  const fetchPlaces = async () => {
    try {
      const { data, error } = await supabase
        .from('places')
        .select('*')
        .order('name')

      if (error) throw error
      setPlaces(data || [])
    } catch (error) {
      console.error('Error fetching places:', error)
    }
  }

  const fetchAreas = async (placeId: string) => {
    try {
      const { data, error } = await supabase
        .from('areas')
        .select('*')
        .eq('place_id', placeId)
        .order('name')

      if (error) throw error
      setAreas(data || [])
    } catch (error) {
      console.error('Error fetching areas:', error)
    }
  }

  const fetchReferralSources = async () => {
    try {
      const { data, error } = await supabase
        .from('referral_sources')
        .select('*')
        .order('name')

      if (error) throw error
      setReferralSources(data || [])
    } catch (error) {
      console.error('Error fetching referral sources:', error)
    }
  }

  const handlePlaceChange = (placeId: string) => {
    setSelectedPlace(placeId)
    setSelectedArea('') // Reset area when place changes
    if (placeId) {
      fetchAreas(placeId)
    } else {
      setAreas([])
    }
  }

  const addNewPlace = async () => {
    if (!newPlaceName.trim()) {
      Alert.alert('هەڵە', 'ناوی شوێن داخڵ بکە')
      return
    }

    try {
      const { data, error } = await supabase
        .from('places')
        .insert([{ name: newPlaceName.trim() }])
        .select()

      if (error) throw error

      setPlaces([...places, data[0]])
      setSelectedPlace(data[0].id)
      setNewPlaceName('')
      setShowAddPlace(false)
      Alert.alert('سەرکەوتوو', 'شوێنی نوێ زیادکرا')
    } catch (error) {
      console.error('Error adding place:', error)
      Alert.alert('هەڵە', 'نەتوانرا شوێنی نوێ زیاد بکرێت')
    }
  }

  const addNewArea = async () => {
    if (!newAreaName.trim() || !selectedPlace) {
      Alert.alert('هەڵە', 'ناوی ناوچە و شوێن دیاری بکە')
      return
    }

    try {
      const { data, error } = await supabase
        .from('areas')
        .insert([{ name: newAreaName.trim(), place_id: selectedPlace }])
        .select()

      if (error) throw error

      setAreas([...areas, data[0]])
      setSelectedArea(data[0].id)
      setNewAreaName('')
      setShowAddArea(false)
      Alert.alert('سەرکەوتوو', 'ناوچەی نوێ زیادکرا')
    } catch (error) {
      console.error('Error adding area:', error)
      Alert.alert('هەڵە', 'نەتوانرا ناوچەی نوێ زیاد بکرێت')
    }
  }

  const addNewReferralSource = async () => {
    if (!newReferralSourceName.trim()) {
      Alert.alert('هەڵە', 'ناوی سەرچاوەی ناردن داخڵ بکە')
      return
    }

    try {
      const { data, error } = await supabase
        .from('referral_sources')
        .insert([{ name: newReferralSourceName.trim() }])
        .select()

      if (error) throw error

      setReferralSources([...referralSources, data[0]])
      setSelectedReferralSource(data[0].id)
      setNewReferralSourceName('')
      setShowAddReferralSource(false)
      Alert.alert('سەرکەوتوو', 'سەرچاوەی ناردنی نوێ زیادکرا')
    } catch (error) {
      console.error('Error adding referral source:', error)
      Alert.alert('هەڵە', 'نەتوانرا سەرچاوەی ناردنی نوێ زیاد بکرێت')
    }
  }

  const handleSave = async () => {
    if (!customerName.trim() || !customerPhone.trim() || !selectedPlace) {
      Alert.alert('هەڵە', 'تکایە هەموو خانە پێویستەکان پڕ بکەرەوە')
      return
    }

    if (!job) return

    setLoading(true)

    try {
      const updateData = {
        customer_name: customerName.trim(),
        customer_phone: customerPhone.trim(),
        place_id: selectedPlace,
        area_id: selectedArea || null,
        referral_source_id: selectedReferralSource || null,
        assigned_installer: assignedInstaller,
        updated_at: new Date().toISOString(),
      }

      const { error } = await supabase
        .from('jobs')
        .update(updateData)
        .eq('id', job.id)

      if (error) throw error

      Alert.alert('سەرکەوتوو', 'زانیاری کڕیار نوێ کرایەوە')
      onSave()
      onClose()
    } catch (error) {
      console.error('Error updating customer:', error)
      Alert.alert('هەڵە', 'نەتوانرا زانیاری کڕیار نوێ بکرێتەوە')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    // Reset form to original values
    if (job) {
      setCustomerName(job.customer_name || '')
      setCustomerPhone(job.customer_phone || '')
      setSelectedPlace(job.place_id || '')
      setSelectedArea(job.area_id || '')
      setSelectedReferralSource(job.referral_source_id || '')
      setAssignedInstaller(job.assigned_installer || 'installer1')
    }
    onClose()
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleCancel}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.headerContent}>
                <Ionicons name="person-circle" size={28} color="#007AFF" />
                <Text style={styles.headerTitle}>دەستکاریکردنی کڕیار</Text>
              </View>
              <Text style={styles.headerSubtitle}>زانیاری کڕیار نوێ بکەرەوە</Text>
            </View>

            {/* Customer Name */}
            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <Ionicons name="person" size={18} color="#007AFF" />
                <Text style={styles.label}>ناوی کڕیار:</Text>
              </View>
              <TextInput
                style={styles.input}
                value={customerName}
                onChangeText={setCustomerName}
                placeholder="ناوی کڕیار داخڵ بکە"
                textAlign="right"
              />
            </View>

            {/* Customer Phone */}
            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <Ionicons name="call" size={18} color="#28a745" />
                <Text style={styles.label}>ژمارەی تەلەفۆن:</Text>
              </View>
              <TextInput
                style={styles.input}
                value={customerPhone}
                onChangeText={setCustomerPhone}
                placeholder="ژمارەی تەلەفۆن داخڵ بکە"
                keyboardType="phone-pad"
                textAlign="right"
              />
            </View>

            {/* Place Selection */}
            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <Ionicons name="location" size={18} color="#FF6B35" />
                <Text style={styles.label}>شوێن:</Text>
              </View>
              <View style={styles.pickerRow}>
                <View style={styles.pickerWrapper}>
                  <Picker
                    selectedValue={selectedPlace}
                    onValueChange={handlePlaceChange}
                    style={styles.picker}
                    itemStyle={styles.pickerItem}
                    mode="dropdown"
                  >
                    <Picker.Item label="شوێن دیاری بکە" value="" />
                    {places.map((place) => (
                      <Picker.Item key={place.id} label={place.name} value={place.id} />
                    ))}
                  </Picker>
                </View>
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={() => setShowAddPlace(true)}
                >
                  <Ionicons name="add" size={20} color="white" />
                  <Text style={styles.addButtonText}>نوێ</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Area Selection */}
            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <Ionicons name="map" size={18} color="#FF6B35" />
                <Text style={styles.label}>ناوچە:</Text>
              </View>
              <View style={styles.pickerRow}>
                <View style={styles.pickerWrapper}>
                  <Picker
                    selectedValue={selectedArea}
                    onValueChange={setSelectedArea}
                    style={styles.picker}
                    itemStyle={styles.pickerItem}
                    mode="dropdown"
                    enabled={!!selectedPlace}
                  >
                    <Picker.Item label="ناوچە دیاری بکە" value="" />
                    {areas.map((area) => (
                      <Picker.Item key={area.id} label={area.name} value={area.id} />
                    ))}
                  </Picker>
                </View>
                <TouchableOpacity
                  style={[styles.addButton, !selectedPlace && styles.addButtonDisabled]}
                  onPress={() => setShowAddArea(true)}
                  disabled={!selectedPlace}
                >
                  <Ionicons name="add" size={20} color="white" />
                  <Text style={styles.addButtonText}>نوێ</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Referral Source Selection */}
            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <Ionicons name="people" size={18} color="#17a2b8" />
                <Text style={styles.label}>سەرچاوەی ناردن:</Text>
              </View>
              <View style={styles.pickerRow}>
                <View style={styles.pickerWrapper}>
                  <Picker
                    selectedValue={selectedReferralSource}
                    onValueChange={setSelectedReferralSource}
                    style={styles.picker}
                    itemStyle={styles.pickerItem}
                    mode="dropdown"
                  >
                    <Picker.Item label="سەرچاوەی ناردن دیاری بکە" value="" />
                    {referralSources.map((source) => (
                      <Picker.Item key={source.id} label={source.name} value={source.id} />
                    ))}
                  </Picker>
                </View>
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={() => setShowAddReferralSource(true)}
                >
                  <Ionicons name="add" size={20} color="white" />
                  <Text style={styles.addButtonText}>نوێ</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Installer Assignment */}
            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <Ionicons name="construct" size={18} color="#fd7e14" />
                <Text style={styles.label}>دیاری کردنی وەستا:</Text>
              </View>
              <View style={styles.pickerRow}>
                <View style={styles.pickerWrapper}>
                  <Picker
                    selectedValue={assignedInstaller}
                    onValueChange={setAssignedInstaller}
                    style={styles.picker}
                    itemStyle={styles.pickerItem}
                    mode="dropdown"
                  >
                    <Picker.Item label="وەستای بەستن یەکەم" value="installer1" />
                    <Picker.Item label="وەستای بەستن دووەم" value="installer2" />
                  </Picker>
                </View>
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.saveButton, loading && styles.buttonDisabled]}
                onPress={handleSave}
                disabled={loading}
              >
                <Ionicons name="checkmark-circle" size={20} color="white" />
                <Text style={styles.saveButtonText}>
                  {loading ? 'خەزنکردن...' : 'خەزنکردن'}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.cancelButton}
                onPress={handleCancel}
                disabled={loading}
              >
                <Ionicons name="close-circle" size={20} color="#666" />
                <Text style={styles.cancelButtonText}>پاشگەزبوونەوە</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      </View>

      {/* Add Place Modal */}
      <Modal
        visible={showAddPlace}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowAddPlace(false)}
      >
        <View style={styles.subModalContainer}>
          <View style={styles.subModalContent}>
            <Text style={styles.subModalTitle}>شوێنی نوێ زیادبکە</Text>
            <TextInput
              style={styles.subModalInput}
              value={newPlaceName}
              onChangeText={setNewPlaceName}
              placeholder="ناوی شوێن داخڵ بکە"
              textAlign="right"
            />
            <View style={styles.subModalButtons}>
              <TouchableOpacity style={styles.subModalSaveButton} onPress={addNewPlace}>
                <Text style={styles.subModalSaveText}>زیادکردن</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.subModalCancelButton}
                onPress={() => setShowAddPlace(false)}
              >
                <Text style={styles.subModalCancelText}>پاشگەزبوونەوە</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Add Area Modal */}
      <Modal
        visible={showAddArea}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowAddArea(false)}
      >
        <View style={styles.subModalContainer}>
          <View style={styles.subModalContent}>
            <Text style={styles.subModalTitle}>ناوچەی نوێ زیادبکە</Text>
            <TextInput
              style={styles.subModalInput}
              value={newAreaName}
              onChangeText={setNewAreaName}
              placeholder="ناوی ناوچە داخڵ بکە"
              textAlign="right"
            />
            <View style={styles.subModalButtons}>
              <TouchableOpacity style={styles.subModalSaveButton} onPress={addNewArea}>
                <Text style={styles.subModalSaveText}>زیادکردن</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.subModalCancelButton}
                onPress={() => setShowAddArea(false)}
              >
                <Text style={styles.subModalCancelText}>پاشگەزبوونەوە</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Add Referral Source Modal */}
      <Modal
        visible={showAddReferralSource}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowAddReferralSource(false)}
      >
        <View style={styles.subModalContainer}>
          <View style={styles.subModalContent}>
            <Text style={styles.subModalTitle}>سەرچاوەی ناردنی نوێ زیادبکە</Text>
            <TextInput
              style={styles.subModalInput}
              value={newReferralSourceName}
              onChangeText={setNewReferralSourceName}
              placeholder="ناوی سەرچاوەی ناردن داخڵ بکە"
              textAlign="right"
            />
            <View style={styles.subModalButtons}>
              <TouchableOpacity style={styles.subModalSaveButton} onPress={addNewReferralSource}>
                <Text style={styles.subModalSaveText}>زیادکردن</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.subModalCancelButton}
                onPress={() => setShowAddReferralSource(false)}
              >
                <Text style={styles.subModalCancelText}>پاشگەزبوونەوە</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </Modal>
  )
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerContent: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginRight: 12,
    textAlign: 'right',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  labelContainer: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginRight: 8,
    textAlign: 'right',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
    textAlign: 'right',
  },
  pickerRow: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
  },
  pickerWrapper: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    backgroundColor: '#f8f9fa',
    marginLeft: 8,
    paddingHorizontal: 8,
    minHeight: 60,
  },
  picker: {
    height: 60,
    fontSize: 15,
    color: '#333',
    textAlign: 'right',
  },
  pickerItem: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
    textAlign: 'right',
    height: 60,
    paddingHorizontal: 15,
    paddingVertical: 15,
  },
  addButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flexDirection: 'row-reverse',
    alignItems: 'center',
  },
  addButtonDisabled: {
    backgroundColor: '#ccc',
  },
  addButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginRight: 4,
  },
  actionButtons: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    marginTop: 24,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  saveButton: {
    backgroundColor: '#28a745',
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
    flexDirection: 'row-reverse',
    alignItems: 'center',
    flex: 1,
    marginLeft: 8,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
    flexDirection: 'row-reverse',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
    textAlign: 'center',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  subModalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  subModalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    width: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  subModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 16,
  },
  subModalInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlign: 'right',
    marginBottom: 16,
  },
  subModalButtons: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
  },
  subModalSaveButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 10,
    flex: 1,
    marginLeft: 8,
  },
  subModalSaveText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  subModalCancelButton: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 10,
    flex: 1,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  subModalCancelText: {
    color: '#666',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
})

export default EditCustomerModal
