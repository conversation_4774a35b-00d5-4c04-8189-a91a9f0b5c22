{"name": "aliduct", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@craftzdog/react-native-buffer": "^6.1.0", "@expo/vector-icons": "^14.1.0", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@supabase/supabase-js": "^1.35.7", "base64-arraybuffer": "^1.0.2", "expo": "~53.0.9", "expo-camera": "^16.1.6", "expo-image-picker": "^16.1.4", "expo-localization": "^16.1.5", "expo-location": "^18.1.5", "expo-notifications": "^0.31.2", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-crypto": "^2.2.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-url-polyfill": "^2.0.0", "react-native-webview": "13.13.5", "stream-browserify": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-crypto", "stream-browserify"], "listUnknownPackages": false}}}, "private": true}