/**
 * Debug Image URL Generation
 * This tests the exact issue with public URL generation
 */

const { createClient } = require('@supabase/supabase-js')

// Your Supabase configuration
const SUPABASE_URL = 'https://emxbbmsieclwlslwgkua.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVteGJibXNpZWNsd2xzbHdna3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ3MzgsImV4cCI6MjA2MzkzMDczOH0.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

// Create a simple test image
function createTestImage() {
  const base64PNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=='
  return Buffer.from(base64PNG, 'base64')
}

async function debugImageUrls() {
  console.log('🔍 Debugging Image URL Generation...\n')

  try {
    // Step 1: Upload a test image
    console.log('1️⃣ Uploading test image...')
    
    const testImageBuffer = createTestImage()
    const testFileName = `debug-test-${Date.now()}.png`
    
    console.log(`Uploading: ${testFileName}`)
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('job-images')
      .upload(testFileName, testImageBuffer, {
        contentType: 'image/png'
      })

    if (uploadError) {
      console.error('❌ Upload failed:', uploadError.message)
      return
    }
    
    console.log('✅ Upload successful!')
    console.log('Upload data:', uploadData)

    // Step 2: Test different ways to get public URL
    console.log('\n2️⃣ Testing public URL generation...')
    
    // Method 1: Standard getPublicUrl
    console.log('Method 1: Standard getPublicUrl')
    const { data: urlData1 } = supabase.storage
      .from('job-images')
      .getPublicUrl(uploadData.path)
    
    console.log('Result 1:', urlData1)

    // Method 2: Manual URL construction
    console.log('\nMethod 2: Manual URL construction')
    const manualUrl = `${SUPABASE_URL}/storage/v1/object/public/job-images/${uploadData.path}`
    console.log('Manual URL:', manualUrl)

    // Method 3: Test with different path formats
    console.log('\nMethod 3: Different path formats')
    const { data: urlData3 } = supabase.storage
      .from('job-images')
      .getPublicUrl(uploadData.path || uploadData.Key || uploadData.fullPath)
    
    console.log('Result 3:', urlData3)

    // Step 3: Test URL accessibility
    console.log('\n3️⃣ Testing URL accessibility...')
    
    const urlsToTest = [
      { name: 'Standard getPublicUrl', url: urlData1?.publicUrl },
      { name: 'Manual construction', url: manualUrl }
    ]

    for (const test of urlsToTest) {
      if (test.url) {
        console.log(`\nTesting ${test.name}: ${test.url}`)
        try {
          const response = await fetch(test.url)
          console.log(`  Status: ${response.status}`)
          console.log(`  Content-Type: ${response.headers.get('content-type')}`)
          console.log(`  Accessible: ${response.ok ? '✅' : '❌'}`)
        } catch (fetchError) {
          console.log(`  ❌ Fetch failed: ${fetchError.message}`)
        }
      } else {
        console.log(`\n${test.name}: ❌ URL is null/undefined`)
      }
    }

    // Step 4: Check bucket configuration
    console.log('\n4️⃣ Checking bucket configuration...')
    
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()
    
    if (bucketsError) {
      console.error('❌ Cannot list buckets:', bucketsError.message)
    } else {
      const jobImagesBucket = buckets.find(b => b.name === 'job-images')
      if (jobImagesBucket) {
        console.log('✅ Bucket configuration:')
        console.log('  Name:', jobImagesBucket.name)
        console.log('  Public:', jobImagesBucket.public)
        console.log('  File size limit:', jobImagesBucket.file_size_limit)
        console.log('  Allowed MIME types:', jobImagesBucket.allowed_mime_types)
      } else {
        console.log('❌ job-images bucket not found')
      }
    }

    // Step 5: List files in bucket
    console.log('\n5️⃣ Listing files in bucket...')
    
    const { data: files, error: listError } = await supabase.storage
      .from('job-images')
      .list()

    if (listError) {
      console.log('❌ Cannot list files:', listError.message)
    } else {
      console.log(`✅ Files in bucket: ${files.length}`)
      
      // Test URL generation for existing files
      if (files.length > 0) {
        console.log('\nTesting URL generation for existing files:')
        files.slice(0, 3).forEach((file, index) => {
          const { data: fileUrlData } = supabase.storage
            .from('job-images')
            .getPublicUrl(file.name)
          
          console.log(`  ${index + 1}. ${file.name}`)
          console.log(`     URL: ${fileUrlData?.publicUrl || 'undefined'}`)
        })
      }
    }

    // Step 6: Clean up test file
    console.log('\n6️⃣ Cleaning up...')
    
    const { error: deleteError } = await supabase.storage
      .from('job-images')
      .remove([uploadData.path])

    if (deleteError) {
      console.log('⚠️ Could not delete test file:', deleteError.message)
    } else {
      console.log('✅ Test file cleaned up')
    }

    console.log('\n📋 DIAGNOSIS:')
    
    if (urlData1?.publicUrl) {
      console.log('✅ Public URL generation is working')
      console.log('💡 The issue might be elsewhere in your app')
    } else {
      console.log('❌ Public URL generation is failing')
      console.log('💡 This explains why images are not showing in your app')
      console.log('💡 Try using manual URL construction as a workaround')
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }
}

// Run the debug
debugImageUrls()
