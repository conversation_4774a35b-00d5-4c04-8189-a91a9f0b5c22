import React, { createContext, useContext, useState, useEffect } from 'react'
import * as SecureStore from 'expo-secure-store'
import { supabase } from '../lib/supabase'

interface User {
  id: string
  username: string
  role: 'admin' | 'dispatch' | 'installer1' | 'installer2'
  display_name: string
}

interface AuthContextType {
  user: User | null
  login: (username: string, password: string, stayLoggedIn?: boolean) => Promise<boolean>
  logout: () => Promise<void>
  loading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Simple password verification (in production, use proper hashing)
const verifyPassword = (inputPassword: string, username: string): boolean => {
  const credentials = {
    'ali': '223344',
    'car': '123455',
    'wasta1': '654321',
    'wasta2': '123456788'
  }
  return credentials[username as keyof typeof credentials] === inputPassword
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkStoredAuth()
  }, [])

  const checkStoredAuth = async () => {
    try {
      const storedUser = await SecureStore.getItemAsync('user')
      if (storedUser) {
        setUser(JSON.parse(storedUser))
      }
    } catch (error) {
      console.error('Error checking stored auth:', error)
    } finally {
      setLoading(false)
    }
  }

  const login = async (username: string, password: string, stayLoggedIn = false): Promise<boolean> => {
    try {
      // Verify password
      if (!verifyPassword(password, username)) {
        return false
      }

      // Get user from database
      const { data: userData, error } = await supabase
        .from('app_users')
        .select('*')
        .eq('username', username)
        .single()

      if (error || !userData) {
        return false
      }

      const userObj: User = {
        id: userData.id,
        username: userData.username,
        role: userData.role,
        display_name: userData.display_name
      }

      setUser(userObj)

      // Store user if stay logged in is enabled
      if (stayLoggedIn) {
        await SecureStore.setItemAsync('user', JSON.stringify(userObj))
      }

      return true
    } catch (error) {
      console.error('Login error:', error)
      return false
    }
  }

  const logout = async () => {
    try {
      await SecureStore.deleteItemAsync('user')
      setUser(null)
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  return (
    <AuthContext.Provider value={{ user, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  )
}
