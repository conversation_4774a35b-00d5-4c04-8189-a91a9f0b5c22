-- =====================================================
-- Supabase Storage RLS Policy Fix for Aliduct App
-- =====================================================
-- Run these commands in Supabase Dashboard > SQL Editor

-- 1. Create RLS policies for storage.buckets table
-- Allow authenticated users to select buckets
CREATE POLICY "Allow authenticated users to select buckets" ON storage.buckets
  FOR SELECT TO authenticated
  USING (true);

-- Allow authenticated users to insert buckets (for bucket creation)
CREATE POLICY "Allow authenticated users to insert buckets" ON storage.buckets
  FOR INSERT TO authenticated
  WITH CHECK (true);

-- 2. Create RLS policies for storage.objects table
-- Allow authenticated users to select objects in job-images bucket
CREATE POLICY "Allow authenticated users to select job-images" ON storage.objects
  FOR SELECT TO authenticated
  USING (bucket_id = 'job-images');

-- Allow authenticated users to insert objects in job-images bucket
CREATE POLICY "Allow authenticated users to insert job-images" ON storage.objects
  FOR INSERT TO authenticated
  WITH CHECK (bucket_id = 'job-images');

-- Allow authenticated users to update objects in job-images bucket
CREATE POLICY "Allow authenticated users to update job-images" ON storage.objects
  FOR UPDATE TO authenticated
  USING (bucket_id = 'job-images')
  WITH CHECK (bucket_id = 'job-images');

-- Allow authenticated users to delete objects in job-images bucket
CREATE POLICY "Allow authenticated users to delete job-images" ON storage.objects
  FOR DELETE TO authenticated
  USING (bucket_id = 'job-images');

-- 3. Create the job-images bucket manually (alternative to app creation)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types) 
VALUES (
  'job-images', 
  'job-images', 
  true, 
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/jpg']
)
ON CONFLICT (id) DO NOTHING;

-- 4. Verify the setup
SELECT 
  'Buckets' as table_name,
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'buckets'

UNION ALL

SELECT 
  'Objects' as table_name,
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'objects';
