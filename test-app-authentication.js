/**
 * Test App Authentication System
 * This tests the specific authentication system your app uses
 */

const { createClient } = require('@supabase/supabase-js')

// Your Supabase configuration
const SUPABASE_URL = 'https://emxbbmsieclwlslwgkua.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVteGJibXNpZWNsd2xzbHdna3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ3MzgsImV4cCI6MjA2MzkzMDczOH0.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

// Test credentials from your app
const testCredentials = {
  'ali': '223344',
  'car': '123455', 
  'wasta1': '654321',
  'wasta2': '123456788'
}

async function testAppAuthentication() {
  console.log('🔐 Testing App Authentication System...\n')

  try {
    // Test 1: Check if app_users table exists and is accessible
    console.log('1️⃣ Testing app_users table access...')
    
    const { data: users, error: usersError } = await supabase
      .from('app_users')
      .select('*')
      .limit(5)

    if (usersError) {
      console.error('❌ Cannot access app_users table:', usersError.message)
      return
    }

    console.log('✅ app_users table accessible')
    console.log(`Found ${users.length} users in database`)
    
    if (users.length > 0) {
      console.log('Users in database:')
      users.forEach(user => {
        console.log(`  - ${user.username} (${user.role}) - ${user.display_name}`)
      })
    }

    // Test 2: Test login for each credential
    console.log('\n2️⃣ Testing login credentials...')
    
    for (const [username, password] of Object.entries(testCredentials)) {
      console.log(`\nTesting login for: ${username}`)
      
      // Verify password (same logic as your app)
      const passwordValid = testCredentials[username] === password
      console.log(`  Password validation: ${passwordValid ? '✅' : '❌'}`)
      
      if (passwordValid) {
        // Try to get user from database
        const { data: userData, error: userError } = await supabase
          .from('app_users')
          .select('*')
          .eq('username', username)
          .single()

        if (userError) {
          console.log(`  ❌ Database lookup failed: ${userError.message}`)
          
          if (userError.message.includes('No rows')) {
            console.log(`  ⚠️ User '${username}' not found in database`)
            console.log(`  💡 You need to add this user to the app_users table`)
          }
        } else {
          console.log(`  ✅ User found in database:`)
          console.log(`    - ID: ${userData.id}`)
          console.log(`    - Role: ${userData.role}`)
          console.log(`    - Display Name: ${userData.display_name}`)
        }
      }
    }

    // Test 3: Check if all required users exist
    console.log('\n3️⃣ Checking required users...')
    
    const requiredUsers = Object.keys(testCredentials)
    const existingUsers = users.map(u => u.username)
    const missingUsers = requiredUsers.filter(u => !existingUsers.includes(u))
    
    if (missingUsers.length > 0) {
      console.log('❌ Missing users in database:')
      missingUsers.forEach(user => {
        console.log(`  - ${user}`)
      })
      
      console.log('\n💡 SOLUTION: Add missing users to app_users table')
      console.log('Run this SQL in Supabase Dashboard:')
      
      missingUsers.forEach(username => {
        const role = username === 'ali' ? 'admin' : 
                    username === 'car' ? 'dispatch' :
                    username.startsWith('wasta') ? 'installer1' : 'installer2'
        
        console.log(`INSERT INTO app_users (username, role, display_name) VALUES ('${username}', '${role}', '${username}');`)
      })
    } else {
      console.log('✅ All required users exist in database')
    }

    // Test 4: Test a complete login flow
    console.log('\n4️⃣ Testing complete login flow...')
    
    const testUser = 'ali'
    const testPassword = '223344'
    
    console.log(`Attempting login: ${testUser} / ${testPassword}`)
    
    // Step 1: Verify password
    const passwordCorrect = testCredentials[testUser] === testPassword
    console.log(`  Password check: ${passwordCorrect ? '✅' : '❌'}`)
    
    if (passwordCorrect) {
      // Step 2: Get user from database
      const { data: loginUser, error: loginError } = await supabase
        .from('app_users')
        .select('*')
        .eq('username', testUser)
        .single()

      if (loginError) {
        console.log(`  ❌ Login failed: ${loginError.message}`)
      } else {
        console.log(`  ✅ Login successful!`)
        console.log(`    - Welcome ${loginUser.display_name}`)
        console.log(`    - Role: ${loginUser.role}`)
        console.log(`    - User ID: ${loginUser.id}`)
      }
    }

    console.log('\n📋 SUMMARY:')
    console.log('Your Supabase connection is working perfectly!')
    console.log('Your authentication system is working!')
    console.log('If you see any ❌ marks above, those are the specific issues to fix.')

  } catch (error) {
    console.error('💥 Unexpected error during authentication test:', error)
  }
}

// Run the authentication test
testAppAuthentication()
