# Download and install Android Emulator Hypervisor Driver
# This script downloads the latest version and installs it

Write-Host "Installing Android Emulator Hypervisor Driver..." -ForegroundColor Green

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "This script needs to run as Administrator to install the driver." -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator', then run this script again." -ForegroundColor Yellow
    exit 1
}

# Download URL for the latest release (v2.2)
$downloadUrl = "https://github.com/google/android-emulator-hypervisor-driver/releases/download/v2.2/aehd-windows_v2_2_0.zip"
$downloadPath = "$env:TEMP\aehd-windows_v2_2_0.zip"
$extractPath = "$env:TEMP\aehd-windows_v2_2_0"

Write-Host "Downloading Android Emulator Hypervisor Driver v2.2..." -ForegroundColor Yellow

try {
    # Download the file
    Invoke-WebRequest -Uri $downloadUrl -OutFile $downloadPath -UseBasicParsing
    Write-Host "Download completed successfully!" -ForegroundColor Green

    # Extract the zip file
    Write-Host "Extracting files..." -ForegroundColor Yellow
    if (Test-Path $extractPath) {
        Remove-Item $extractPath -Recurse -Force
    }
    Expand-Archive -Path $downloadPath -DestinationPath $extractPath -Force

    # Find the MSI installer
    $msiFile = Get-ChildItem -Path $extractPath -Filter "*.msi" -Recurse | Select-Object -First 1

    if ($msiFile) {
        Write-Host "Installing hypervisor driver..." -ForegroundColor Yellow
        Write-Host "MSI file: $($msiFile.FullName)" -ForegroundColor Cyan

        # Install the MSI silently
        $installArgs = "/i `"$($msiFile.FullName)`" /quiet /norestart"
        $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $installArgs -Wait -PassThru

        if ($process.ExitCode -eq 0) {
            Write-Host "✅ Android Emulator Hypervisor Driver installed successfully!" -ForegroundColor Green
            Write-Host ""
            Write-Host "The driver has been installed. You may need to restart your computer for it to take effect." -ForegroundColor Yellow
            Write-Host ""
            Write-Host "After restart, you can test the emulator with:" -ForegroundColor Cyan
            Write-Host "emulator -avd Expo_Test_Device" -ForegroundColor White
        } else {
            Write-Host "❌ Installation failed with exit code: $($process.ExitCode)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Could not find MSI installer in the downloaded package." -ForegroundColor Red
    }

    # Clean up
    Write-Host "Cleaning up temporary files..." -ForegroundColor Yellow
    Remove-Item $downloadPath -Force -ErrorAction SilentlyContinue
    Remove-Item $extractPath -Recurse -Force -ErrorAction SilentlyContinue

} catch {
    Write-Host "❌ Error occurred: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Installation process completed!" -ForegroundColor Green
