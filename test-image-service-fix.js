/**
 * Test ImageUploadService Fix
 * This tests the actual ImageUploadService with the fix
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Your Supabase configuration
const SUPABASE_URL = 'https://emxbbmsieclwlslwgkua.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVteGJibXNpZWNsd2xzbHdna3VhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzNTQ3MzgsImV4cCI6MjA2MzkzMDczOH0.1H9EQFeGdRFQBcMxDtYcuH87uLGNr4_81hUKzr0ENLs'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

// Simulate the ImageUploadService logic with the fix
class TestImageUploadService {
  static BUCKET_NAME = 'job-images'

  static async uploadImage(imageBuffer, fileName) {
    try {
      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, imageBuffer, {
          contentType: 'image/png',
          upsert: false
        })

      if (error) {
        console.error('Upload error:', error)
        return {
          success: false,
          error: `Upload failed: ${error.message}`
        }
      }

      // Get public URL - fix: handle different response formats
      let filePath = data.path || data.Key || data.fullPath
      
      // Remove bucket prefix if present (data.Key includes bucket name)
      if (filePath && filePath.startsWith(`${this.BUCKET_NAME}/`)) {
        filePath = filePath.substring(`${this.BUCKET_NAME}/`.length)
      }
      
      const { data: urlData } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(filePath)

      console.log('Upload successful:', {
        uploadData: data,
        originalPath: data.path || data.Key || data.fullPath,
        cleanedPath: filePath,
        urlData: urlData,
        publicUrl: urlData.publicUrl || urlData.publicURL
      })

      // Handle different property names for public URL
      const publicUrl = urlData.publicUrl || urlData.publicURL

      return {
        success: true,
        url: publicUrl
      }

    } catch (error) {
      console.error('Image upload service error:', error)
      return {
        success: false,
        error: 'Service error'
      }
    }
  }
}

// Create a simple test image
function createTestImage() {
  const base64PNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=='
  return Buffer.from(base64PNG, 'base64')
}

async function testImageServiceFix() {
  console.log('🔧 Testing ImageUploadService Fix...\n')

  try {
    // Test 1: Upload using the fixed service
    console.log('1️⃣ Testing upload with fixed service...')
    
    const testImageBuffer = createTestImage()
    const testFileName = `service-test-${Date.now()}.png`
    
    console.log(`Uploading: ${testFileName}`)
    
    const result = await TestImageUploadService.uploadImage(testImageBuffer, testFileName)
    
    if (result.success) {
      console.log('✅ Upload successful!')
      console.log('Public URL:', result.url)
      
      // Test 2: Verify the URL is accessible
      console.log('\n2️⃣ Testing URL accessibility...')
      
      if (result.url) {
        try {
          const response = await fetch(result.url)
          console.log(`Status: ${response.status}`)
          console.log(`Content-Type: ${response.headers.get('content-type')}`)
          console.log(`Accessible: ${response.ok ? '✅' : '❌'}`)
          
          if (response.ok) {
            console.log('🎉 SUCCESS! Image URL is working!')
          }
        } catch (fetchError) {
          console.log(`❌ Fetch failed: ${fetchError.message}`)
        }
      } else {
        console.log('❌ URL is null/undefined')
      }
      
      // Test 3: Clean up
      console.log('\n3️⃣ Cleaning up...')
      
      // Extract filename from URL for cleanup
      const urlParts = result.url.split('/')
      const fileNameFromUrl = urlParts[urlParts.length - 1]
      
      const { error: deleteError } = await supabase.storage
        .from('job-images')
        .remove([fileNameFromUrl])

      if (deleteError) {
        console.log('⚠️ Could not delete test file:', deleteError.message)
      } else {
        console.log('✅ Test file cleaned up')
      }
      
    } else {
      console.log('❌ Upload failed:', result.error)
    }

    console.log('\n📋 CONCLUSION:')
    
    if (result.success && result.url) {
      console.log('✅ ImageUploadService fix is working!')
      console.log('✅ Your app should now properly display images')
      console.log('💡 Try adding a new job with images in your app')
    } else {
      console.log('❌ Fix did not resolve the issue')
      console.log('💡 Further investigation needed')
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }
}

// Run the test
testImageServiceFix()
